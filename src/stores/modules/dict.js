import { defineStore } from 'pinia';
// 导入维护模块的 API
import API from '@/api/maintainance';
import { ElMessage } from 'element-plus'; // 引入 ElMessage 用于错误提示

export const useDictStore = defineStore('dict', {
  state: () => ({
    // 使用对象存储不同类型的字典，键为字典类型，值为数组
    dictData: {},
    // 存储每种字典的加载状态
    loadingStatus: {},
    // 存储字典的map对象
    dictMap: {}
  }),
  getters: {
    /**
     * 根据类型获取字典数据
     * @param {object} state - Pinia state
     * @returns {function(string): array} - 返回一个函数，接收字典类型，返回对应的字典数组
     */
    getDictByType: (state) => (type, fieldMap = { label: 'label', value: 'value' }) => {
      const dictData = state.dictData[type] || [];
      return dictData.map(item => ({
        [fieldMap.label]: item.itemText,
        [fieldMap.value]: item.itemValue
      }));
    },
    /**
     * 根据类型获取字典map数据
     * @param {object} state - Pinia state
     * @returns {function(string): object} - 返回一个函数，接收字典类型，返回对应的字典map对象
     */
    getDictMapByType: (state) => (type) => {
      return state.dictMap[type] || {};
    },
    /**
     * 检查特定类型的字典是否正在加载
     * @param {object} state - Pinia state
     * @returns {function(string): boolean} - 返回一个函数，接收字典类型，返回加载状态
     */
    isLoading: (state) => (type) => {
      return !!state.loadingStatus[type];
    },
  },
  actions: {
    /**
     * 异步获取并设置指定类型或类型数组的字典数据
     * 如果某个类型的数据已存在或正在加载，则跳过该类型
     * @param {string|string[]} types - 需要获取的字典类型或类型数组
     */
    async fetchDict(types) {
      const requestedTypes = Array.isArray(types) ? types : [types]; // 统一处理为数组
      const typesToFetch = [];

      // 筛选出需要获取的类型，并立即标记为加载中
      for (const type of requestedTypes) {
        // 检查类型是否有效（非空字符串等）
        if (!type) {
            console.warn('请求的字典类型为空，已跳过');
            continue;
        }
        if (!this.dictData[type] && !this.loadingStatus[type]) {
          typesToFetch.push(type);
          this.loadingStatus[type] = true; // 标记为加载中
        }
      }

      // 如果没有需要获取的新类型，则直接返回
      if (typesToFetch.length === 0) {
        return;
      }

      try {
        // 调用 getMaintainanceDicts 接口，传入需要获取的类型数组
        const res = await API.getMaintainanceDicts(typesToFetch); // 使用正确的接口名

        if (res.data.success && res.data.result) {
          const returnedData = res.data.result;
          // const returnedTypes = Object.keys(returnedData);

          // 更新成功获取到的字典数据
          for (const type of typesToFetch) {
            const dictCodes =  returnedData.filter(item => item.dictCode === type)
            this.dictData[type] = dictCodes; // 更新数据，空数据也接受
            // 将字典数据转成map对象
            this.dictMap[type] = {};
            dictCodes.forEach(item => {
              this.dictMap[type][item.itemValue] = item.itemText;
            });
            this.loadingStatus[type] = false; // 标记加载完成
          }
        } else {
          // 整个批量请求失败
          const errorMsg = `批量获取字典失败: ${res.error || res.message || '未知错误'}`;
          ElMessage.error(errorMsg);
          // 将所有尝试获取的类型标记为加载完成（失败）并设置为空数组
          for (const type of typesToFetch) {
            this.dictData[type] = [];
            this.dictMap[type] = {};
            this.loadingStatus[type] = false;
          }
        }
      } catch (error) {
        // 网络请求或代码执行异常
        const errorMsg = `批量获取字典时网络或程序出错`;
        console.error(errorMsg + ':', error);
        ElMessage.error(errorMsg);
        // 将所有尝试获取的类型标记为加载完成（异常）并设置为空数组
        for (const type of typesToFetch) {
          this.dictData[type] = [];
          this.dictMap[type] = {};
          this.loadingStatus[type] = false;
        }
      }
    },

    /**
     * 清空指定类型或所有类型的字典数据
     * @param {string} [type] - 可选，要清空的字典类型。如果省略，则清空所有字典。
     */
    clearDict(type) {
      if (type) {
        delete this.dictData[type];
        delete this.dictMap[type];
        delete this.loadingStatus[type]; // 同时清除加载状态
      } else {
        this.dictData = {};
        this.dictMap = {};
        this.loadingStatus = {};
      }
    },

    /**
     * 强制重新获取指定类型的字典数据
     * @param {string} type - 需要重新获取的字典类型
     */
    async forceFetchDict(type) {
        // 强制获取单个时，也调用批量接口，保持一致性
        // 先清除旧数据和加载状态
        this.clearDict(type);
        // 再调用 fetchDict 获取
        await this.fetchDict([type]);
    }
  },
});
