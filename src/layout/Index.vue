<template>
	<el-container class="common-layout">
		<Asider class="layout-aside" />
		<el-container class="layout-rcont">
			<el-header class="layout-header"><Header /></el-header>
			<el-main class="layout-main">
				<router-view v-slot="{ Component, route }">
					<keep-alive>
						<component :is="Component" v-if="route.meta.keepAlive" :key="route.fullPath" />
					</keep-alive>
					<component :is="Component" v-if="!route.meta.keepAlive" :key="route.fullPath" />
				</router-view>
			</el-main>
			<!-- <el-footer><Footer /></el-footer> -->
		</el-container>
	</el-container>
</template>

<script setup>
import Asider from '@/layout/Aside.vue';
import Header from '@/layout/Header.vue';
// import Footer from '@/layout/Footer.vue';
</script>

<style scoped lang="less">
.common-layout {
	margin: 0 auto;
	height: 100vh;
	background: @base-background-color;
	min-width: @main-base-width;

	.layout-aside {
		position: sticky;
		top: 0;
		left: 0;
		z-index: 999;
	}

	.layout-rcont {
		.layout-header {
			position: sticky;
			top: 0;
			left: 0;
			width: 100%;
			padding: 0;
			background: @layout-background-color;
			-webkit-box-shadow: 0 1px 4px rgb(0 21 41 / 8%);
			box-shadow: 0 1px 4px rgb(0 21 41 / 8%);
			z-index: 888;
		}

		.layout-main {
			display: flex;
			flex-direction: column;
			flex: 1;
			height: 100%;
		}
	}
}
</style>
