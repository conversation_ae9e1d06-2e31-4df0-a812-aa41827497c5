<template>
  <div class="flex-column-start">
    <div class="logo" @click="toHome()">
      {{ isCollapse ? $SHORTNAME : $BASENAME }}
    </div>
    <el-icon class="collapse" @click="isCollapse = !isCollapse">
      <Fold v-if="!isCollapse" /><Expand v-else />
    </el-icon>

    <el-menu
      class="menus"
      @select="handleMenuSelect"
      v-loading="loading"
      
      :default-active="activeInx"
      :collapse="isCollapse"
      :collapse-transition="false"
      :style="isCollapse ? 'min-width: 0' : ''"
    >
      <el-sub-menu
        v-for="(item, index) in menuArr"
        :key="index"
        :index="String(index)"
      >
        <template #title>
          <el-icon><Menu /></el-icon>
          <span>{{ item.name }}</span>
        </template>
        <el-menu-item
          v-for="(itm, inx) in item.children"
          :key="inx"
          :index="itm.url"
        >
          {{ itm.name }}
        </el-menu-item>
      </el-sub-menu>
    </el-menu>
  </div>
</template>

<script type="text/javascript">
import { useStore } from "@/stores";

export default {
  name: "Asider",
  data() {
    return {
      menuArr: [],
      loading: true,
      activeInx: "",
      isCollapse: false,
      menu: [
        {
          url: "",
          parentId: 1,
          name: "流程管理",
          level: 1,
          children: [
            { name: "流程分类", level: 2, url: "/processManage/category" },
            { name: "表单配置", level: 2, url: "/processManage/form" },
            { name: "流程模型", level: 2, url: "/processManage/model" },
            { name: "部署管理", level: 2, url: "/processManage/deploy" },
          ],
        },
        {
          url: "",
          parentId: 0,
          name: "工作管理",
          level: 1,
          children: [
            { name: "新建流程", level: 2, url: "/workManage/newProcess" },
            { name: "我的流程", level: 2, url: "/workManage/myProcess" },
            { name: "待办任务", level: 2, url: "/workManage/todo" },
            { name: "已办任务", level: 2, url: "/workManage/finished" },
          ],
        },
        {
          url: "",
          parentId: 0,
          name: "基础数据",
          level: 1,
          children: [
            { name: "组织架构", level: 2, url: "/baseData/organizationChart" },
            { name: "仓库管理", level: 2, url: "/baseData/warehouseManage" },
            { name: "备件属性", level: 2, url: "/baseData/partProp" },
            { name: "备件保修期", level: 2, url: "/baseData/partWarranty" },
            { name: "备件替换关系", level: 2, url: "/baseData/partReplace" },
            { name: "调拨圈管理", level: 2, url: "/baseData/dialRing" },
            { name: "字典管理", level: 2, url: "/baseData/dictManage" },
            { name: "库位管理", level: 2, url: "/baseData/inventoryManage" },
          ],
        },
        {
          url: "",
          parentId: 3,
          name: "备件监控",
          level: 1,
          children: [
            {
              name: "工单备件监控",
              level: 2,
              url: "/sparePartMonitor/sparemonitor",
            },
            {
              name: "订单监控",
              level: 2,
              url: "/sparePartMonitor/ordermonitor",
            },
            {
              name: "发货监控",
              level: 2,
              url: "/sparePartMonitor/sendgoodsmonitor",
            },
            {
              name: "退返监控",
              level: 2,
              url: "/sparePartMonitor/returnmonitor",
            },
            {
              name: "现有库存查看",
              level: 2,
              url: "/sparePartMonitor/currentRepertoryView",
            },
            {
              name: "服务商账单流水",
              level: 2,
              url: "/sparePartMonitor/billFlowQuery",
            },
            {
              name: "借件审核",
              level: 2,
              url: "/sparePartMonitor/lendOrderAudit",
            },
          ],
        },
        {
          url: "",
          parentId: 0,
          name: "备件管理",
          level: 1,
          children: [
            {
              name: "储备库调拨订单",
              level: 2,
              url: "/sparePartsManage/transferOrder",
            },
            {
              name: "中心仓入库",
              level: 2,
              url: "/sparePartsManage/centralWarehouseStorage",
            },
          ],
        },
      ],
    };
  },

  // 监听路由变化，菜单重新选中
  watch: {
    $route: {
      handler(newRoute, oldRoute) {
        this.getMenus();
      },
      deep: true, // 深度监听，确保query参数的变化也能触发回调
    },
  },

  mounted() {
    this.getMenus();
  },

  methods: {
    handleMenuSelect(index) {
      let targetItem = null;
      for (const menuGroup of this.menuArr) {
        const found = menuGroup.children.find((child) => child.url === index);
        if (found) {
          targetItem = found;
          break;
        }
      }
      if (!targetItem) return;
      const isHttpUrl = /^https?:\/\//.test(targetItem.url);
      if (isHttpUrl) {
        // 打开外部链接
        // window.open(targetItem.url, "_blank", "noopener,noreferrer");
        // // this.$router.push('/')
        // window.location.href = "/";
        // 弹出确认框，提示即将打开外部链接
        this.$confirm("您即将离开此页面，是否继续？", "提示", {
          confirmButtonText: "继续",
          cancelButtonText: "取消",
          type: "warning",
          closeOnClickModal: false,
          closeOnPressEscape: false,
        })
          .then(() => {
            // 用户点击继续，打开新标签页并刷新当前页跳转首页
            window.open(targetItem.url, "_blank", "noopener,noreferrer");
            window.location.href = "/";
          })
          .catch(() => {
            // 用户点击取消，返回上一级路由
            // this.$router.back();
            window.location.href = "/";
          });
      } else {
        // this.getMenus();
        this.$router.push(targetItem.url);
      }
    },
    toHome() {
      window.location.href = "/";
    },

    getMenus() {
      const store = useStore();
      this.menuArr = store.menus;
      !!this.menuArr &&
        this.menuArr.some((item) => {
          return item.children.some((e) => {
            if (this.$route.fullPath.includes(e.url)) {
              this.activeInx = e.url;
              return true;
            }
          });
        });
      // this.menuArr.push(...this.menu)
      setTimeout(() => {
        this.loading = false;
      }, 100);
    },
  },
};
</script>

<style scoped lang="less">
.logo {
  .flex-row-center-center();
  padding: 0 10px;
  width: 100%;
  text-align: center;
  height: 60px;
  background: @base-color;
  color: @base-font-color-white;
  cursor: pointer;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.collapse {
  position: absolute;
  right: -40px;
  top: 22px;
  cursor: pointer;
  color: @base-font-color;
}

.menus {
  flex: 1;
  overflow: hidden;
  overflow-y: auto;
  min-width: @menus-base-width;
  background: @layout-background-color;

  .el-sub-menu__title span {
    box-sizing: border-box;
    padding-right: 15px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
