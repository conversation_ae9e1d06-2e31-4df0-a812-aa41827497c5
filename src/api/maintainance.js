import {
  get,
  post
} from "@/axios/http";

import qs from "qs";

export default {
  
  // 获取字典
  getMaintainanceDict: (dictCode) => {
    return get(`/hdsapi/light/operation/dict/items/${dictCode}`);
  },
  getMaintainanceDicts: (dictCodes) => {
    return post(`/hdsapi/light/operation/dict/items/batch`, dictCodes, 1);
  },

  // 方案管理
  // 新增方案
  addSolution: (params) => {
    return post("/hdsapi/light/operation/solution/add", params, 1);
  },
  // 删除方案
  deleteSolution: (params) => {
    return post("/hdsapi/light/operation/solution/delete", params);
  },
  // 修改方案
  editSolution: (params) => {
    return post("/hdsapi/light/operation/solution/edit", params, 1);
  },
  // 获取方案详情
  getSolution: (params) => {
    return get("/hdsapi/light/operation/solution/get", params);
  },
  // 分页查询方案列表
  getSolutionList: (params) => {
    return get("/hdsapi/light/operation/solution/list", params);
  },
  // 分页查询方案列表
  getSolutionPage: (params) => {
    return get("/hdsapi/light/operation/solution/page", params, 1);
  },

  // 方案分类管理
  // 获取子分类列表
  getSolutionCategoryChildren: (parentId) => {
    return get(`/hdsapi/light/operation/solution-category/children/${parentId}`);
  },
  // 创建分类
  createSolutionCategory: (params) => {
    return post("/hdsapi/light/operation/solution-category/create", params, 1);
  },
  // 删除分类
  deleteSolutionCategory: (id) => {
    return post(`/hdsapi/light/operation/solution-category/delete/${id}`);
  },
  // 获取分类详情
  getSolutionCategoryDetail: (id) => {
    return get(`/hdsapi/light/operation/solution-category/detail/${id}`);
  },
  // 获取所有分类
  getSolutionCategoryList: () => {
    return get("/hdsapi/light/operation/solution-category/list");
  },
  // 获取分类路径
  getSolutionCategoryPath: (path) => {
    return get(`/hdsapi/light/operation/solution-category/path/${path}`);
  },
  // 获取分类树结构
  getSolutionCategoryTree: () => {
    return get("/hdsapi/light/operation/solution-category/tree");
  },
  // 更新分类
  updateSolutionCategory: (id, params) => {
    return post(`/hdsapi/light/operation/solution-category/update/${id}`, params, 1);
  },

  // 故障管理
  // 新增故障
  addFaultCategory: (params) => {
    return post("/hdsapi/light/operation/fault/add", params, 1);
  },
  // 删除故障
  deleteFaultCategory: (params) => {
    return post("/hdsapi/light/operation/fault/delete", params);
  },
  // 修改故障
  editFaultCategory: (params) => {
    return post("/hdsapi/light/operation/fault/edit", params, 1);
  },
  // 更新故障状态
  updateFaultCategoryStatus: (params) => {
    return post("/hdsapi/light/operation/fault/updateStatus", params);
  },
  // 获取故障详情
  getFaultCategory: (params) => {
    return get("/hdsapi/light/operation/fault/get", params);
  },
  // 获取故障列表
  getFaultCategoryList: (params) => {
    return get("/hdsapi/light/operation/fault/list", params);
  },
  // 分页查询故障列表
  getFaultCategoryPage: (params) => {
    return get("/hdsapi/light/operation/fault/page", params);
  },

  // 巡检配置管理
  // 新增巡检配置
  addInspectionConfig: (params) => {
    return post("/hdsapi/light/operation/inspection-config/add", params, 1);
  },
  // 删除巡检配置
  deleteInspectionConfig: (params) => {
    return post("/hdsapi/light/operation/inspection-config/delete", params);
  },
  // 修改巡检配置
  editInspectionConfig: (params) => {
    return post("/hdsapi/light/operation/inspection-config/edit", params, 1);
  },
  // 获取巡检配置详情
  getInspectionConfig: (params) => {
    return get("/hdsapi/light/operation/inspection-config/get", params);
  },
  // 获取巡检配置列表
  getInspectionConfigList: (params) => {
    return get("/hdsapi/light/operation/inspection-config/list", params);
  },
  // 分页查询巡检配置列表
  getInspectionConfigPage: (params) => {
    return get("/hdsapi/light/operation/inspection-config/page", params);
  },
  // 更新巡检配置状态
  updateInspectionConfigStatus: (params) => {
    return post("/hdsapi/light/operation/inspection-config/updateStatus", params);
  },

  // 工单配置管理
  // 新增工单配置
  addWorkOrderConfig: (params) => {
    return post("/hdsapi/light/operation/work-order-config/add", params, 1);
  },
  // 删除工单配置
  deleteWorkOrderConfig: (params) => {
    return post("/hdsapi/light/operation/work-order-config/delete", params);
  },
  // 修改工单配置
  editWorkOrderConfig: (params) => {
    return post("/hdsapi/light/operation/work-order-config/edit", params, 1);
  },
  // 获取工单配置详情
  getWorkOrderConfig: (params) => {
    return get("/hdsapi/light/operation/work-order-config/get", params);
  },
  // 获取工单配置列表
  getWorkOrderConfigList: (params) => {
    return get("/hdsapi/light/operation/work-order-config/list", params);
  },
  // 分页查询工单配置列表
  getWorkOrderConfigPage: (params) => {
    return get("/hdsapi/light/operation/work-order-config/page", params);
  },
  // 更新工单配置状态
  updateWorkOrderConfigStatus: (params) => {
    return post("/hdsapi/light/operation/work-order-config/updateStatus", params);
  },

  // 工单管理
  // 审核通过工单
  auditPassWorkOrder: (orderCode, params) => {
    return post(`/hdsapi/light/operation/workOrder/${orderCode}/audit-pass`, params, 1);
  },
  // 审核驳回工单
  auditRejectWorkOrder: (params) => {
    return post(`/hdsapi/light/operation/workOrder/audit-reject`, params, 1);
  },
  // 下发工单
  dispatchWorkOrder: (orderCode, params) => {
    return post(`/hdsapi/light/operation/workOrder/${orderCode}/dispatch`, params, 1);
  },
  // 批量审核通过工单
  batchAuditPassWorkOrder: (params) => {
    return post(`/hdsapi/light/operation/workOrder/batch-audit-pass`, params, 1);
  },
  // 关闭工单
  closeWorkOrder: (params) => {
    return post(`/hdsapi/light/operation/workOrder/close`, params, 1);
  },
  
  // 分页查询工单效率统计
  getWorkOrderEfficiencyPage: (params) => {
    return get(`/hdsapi/light/operation/workOrder/efficiency-op`, params);
  },
  // 工单效率统计总计
  getWorkOrderEfficiencyStatistic: (params) => {
    return get(`/hdsapi/light/operation/workOrder/efficiency-total`, params);
  },
  // 获取工单详情
  getWorkOrderDetail: (id) => {
    return get(`/hdsapi/light/operation/workOrder/get/${id}`);
  },
  // 根据运维单号获取工单详情
  getWorkOrderByOrderCode: (orderCode) => {
    return get(`/hdsapi/light/operation/workOrder/getByOrderCode/${orderCode}`);
  },
  // 处理工单
  handleWorkOrder: (params) => {
    return post(`/hdsapi/light/operation/workOrder/handle`, params, 1);
  },
  // 已处理工单
  getHandledWorkOrderPage: (params) => {
    return get(`/hdsapi/light/operation/workOrder/handled/page`, params);
  },
  // 工单列表
  getWorkOrderList: (params) => {
    return get("/hdsapi/light/operation/workOrder/list", params);
  },
  // 判断当前用户是否为分中心用户
  getIsSubCenterUser: () => {
    return get("/hdsapi/light/operation/workOrder/loginUser/isSubCenterUser");
  },
  // 分页查询我的提报工单列表
  getMySubmittedWorkOrderPage: (params) => {
    return get("/hdsapi/light/operation/workOrder/my-submitted", params);
  },
  // 分页查询工单列表
  getWorkOrderPage: (params) => {
    return get("/hdsapi/light/operation/workOrder/page", params);
  },
  // 提报工单
  submithWorkOrder: (params) => {
    return post(`/hdsapi/light/operation/workOrder/submit`, params, 1);
  },
  // 分页查询待审核工单列表
  getWorkOrderToAuditPage: (params) => {
    return get("/hdsapi/light/operation/workOrder/to-audit", params);
  },
  // 分页查询待下发工单列表
  getWorkOrderToDispatchPage: (params) => {
    return get("/hdsapi/light/operation/workOrder/to-dispatch", params);
  },

  // 电站管理
  // 获取电站详情
  getStationDetail: (params) => {
    return get("/hdsapi/light/operation/station/get", params);
  },
  // 根据电站编码获取电站详情
  getStationByStationCode: (params) => {
    return get("/hdsapi/light/operation/station/getByStationCode", params);
  },
  // 电站列表
  getStationList: (params) => {
    return get("/hdsapi/light/operation/station/list", params);
  },
  // 分页查询电站列表
  getStationPage: (params) => {
    return get("/hdsapi/light/operation/station/page", params);
  },
  // 同步电站数据
  syncStationData: (params) => {
    return post("/hdsapi/light/operation/station/sync", params, 1);
  },
  // 获取逆变器实时数据列表
  getStationInverterData: (params) => {
    return get("/hdsapi/light/operation/station/inverter/data", params);
  },
  // 更新MPPT数据
  updateStationInverterMppt: (params) => {
    return post(`/hdsapi/light/operation/station/inverter/mppt/update`, params, 1);
  },
  // 获取逆变器发电量数据
  getStationInverterElecData: (params) => {
    return get("/hdsapi/light/operation/station/inverter/elec-data", params);
  },
  // 获取电站辐射数据
  getStationInverterRadiationData: (params) => {
    return get("/hdsapi/light/operation/station/inverter/weather-radiation-data", params);
  },
  // 获取电站逆变器列表
  getStationInverterList: (params) => {
    return get("/hdsapi/light/operation/station/inverter/list", params);
  },
  // 更新电站信息
  updateStationLocation: (params) => {
    return post(`/hdsapi/light/operation/station/location/update`, params, 1);
  },
  // 获取逆变器MPPT数据列表
  getStationInverterMpptData: (params) => {
    return get("/hdsapi/light/operation/station/inverter/mppt-data", params);
  },

  // 巡检计划管理
  // 创建巡检计划
  createInspectionPlan: (params) => {
    return post("/hdsapi/light/operation/inspection/plan/create", params, 1);
  },
  // 删除巡检计划
  deleteInspectionPlan: (params) => {
    return post("/hdsapi/light/operation/inspection/plan/delete", params, 1);
  },
  // 获取巡检计划详情
  getInspectionPlan: (params) => {
    return get("/hdsapi/light/operation/inspection/plan/get", params);
  },
  // 获取巡检计划列表
  getInspectionPlanList: (params) => {
    return get("/hdsapi/light/operation/inspection/plan/list", params);
  },
  // 分页查询巡检计划列表
  getInspectionPlanPage: (params) => {
    return get("/hdsapi/light/operation/inspection/plan/page", params);
  },
  // 更新巡检计划
  updateInspectionPlan: (params) => {
    return post("/hdsapi/light/operation/inspection/plan/update", params, 1);
  },
  // 巡检工单管理
  // 根据工单编号获取巡检工单
  getInspectionWorkOrder: (params) => {
    return get("/hdsapi/light/operation/inspection/work-order/get", params);
  },
  // 获取巡检工单列表
  getInspectionWorkOrderList: (params) => {
    return get("/hdsapi/light/operation/inspection/work-order/list", params);
  },
  // 分页查询巡检工单列表
  getInspectionWorkOrderPage: (params) => {
    return get("/hdsapi/light/operation/inspection/work-order/page", params);
  },
  // 巡检工单统计（按运维商、地区）
  getInspectionWorkOrderStatistics: (params) => {
    return get("/hdsapi/light/operation/inspection/work-order/statistics", params);
  },
  // 巡检计划统计汇总
  getInspectionWorkOrderStatisticsTotal: (params) => {
    return get("/hdsapi/light/operation/inspection/work-order/statistics/total", params);
  },
  // 报表配置管理
  // 获取报表配置详情
  getReportConfig: (params) => {
    return get(`/hdsapi/light/operation/report-config/detail`, params);
  },
  // 获取报表配置详情
  getReportConfigDataFields: (params) => {
    return get(`/hdsapi/light/operation/report-config/data-fields/list-by-type`, params);
  },
  // 创建报表配置
  createReportConfig: (params) => {
    return post('/hdsapi/light/operation/report-config/create', params, 1);
  },
  // 删除报表配置
  deleteReportConfig: (params) => {
    return post(`/hdsapi/light/operation/report-config/delete`, params);
  },
  // 获取当前用户的报表配置列表
  getMyReportConfigList: () => {
    return get('/hdsapi/light/operation/report-config/my-list');
  },
  // 分页查询报表配置
  getReportConfigPage: (params) => {
    return get('/hdsapi/light/operation/report-config/page', params);
  },
  // 修改报表配置
  updateReportConfig: (params) => {
    return post('/hdsapi/light/operation/report-config/update', params, 1);
  },
  // 更新报表配置状态
  updateReportConfigStatus: (params) => {
    return post(`/hdsapi/light/operation/report-config/update-status`, params);
  },
  // 根据报表配置获取报表
  getReportCenterConfigData: (params) => {
    return get('/hdsapi/light/operation/report-center/get-by-config', params);
  },
  // 获取当前用户的报表配置列表
  getReportCenterConfigList: () => {
    return get('/hdsapi/light/operation/report-center/my-config-list');
  },

  // 电站电费管理
  // 分页查询电站电费账单（不包含电站信息）
  getStationElecBillOnlyPage: (params) => {
    return get("/hdsapi/light/operation/station-elec-bill/bill-only/page", params);
  },
  // 根据ID查询电站电费账单
  getStationElecBillDetail: (params) => {
    return get("/hdsapi/light/operation/station-elec-bill/detail", params);
  },
  exportStationElecBill: (params) => {
    return get(`/hdsapi/light/operation/station-elec-bill/export?${qs.stringify(params, { arrayFormat: 'repeat' })}`, null, 3);
  },
  // 分页查询电站电费账单（包含电站信息）
  getStationElecBillPage: (params) => {
    return get(`/hdsapi/light/operation/station-elec-bill/page?${qs.stringify(params, { arrayFormat: 'repeat' })}`);
  },
  // 获取电站电费汇总信息
  getStationElecBillSummary: (params) => {
    return get(`/hdsapi/light/operation/station-elec-bill/summary?${qs.stringify(params, { arrayFormat: 'repeat' })}`);
  },
  // 更新电站电费账单
  updateStationElecBill: (params) => {
    return post("/hdsapi/light/operation/station-elec-bill/update", params, 1);
  },
  // 批量更新电费账单
  batchUpdateElecBill: (params) => {
    return post("/hdsapi/light/operation/station-elec-bill/batch-update", params, 1);
  },
  // 按年份查询电站电费账单
  getStationElecBillByYear: (params) => {
    return get("/hdsapi/light/operation/elec-price/by-year", params);
  },
  // 创建电站电费账单
  createElecBill: (params) => {
    return post("/hdsapi/light/operation/elec-price/create", params, 1);
  },
  // 查询电站电费账单详情
  getElecPriceDetail: (params) => {
    return get("/hdsapi/light/operation/elec-price/detail", params);
  },
  // 下载电费单价导入模板
  downloadElecPriceTemplate: () => {
    return get("/hdsapi/light/operation/elec-price/downloadTemplate", null, 3);
  },
  // 导出电费单价数据
  exportElecPrice: (params) => {
    return get("/hdsapi/light/operation/elec-price/export", params, 3);
  },
  // 导入电费单价数据
  importElecPrice: (params) => {
    return post("/hdsapi/light/operation/elec-price/import", params, 5);
  },
  // 更新电站电费账单
  updateElecPrice: (params) => {
    return post("/hdsapi/light/operation/elec-price/update", params, 1);
  },

  // 消息中心管理
  // 根据ID获取消息详情
  getAllMessagePage: (params) => {
    return get("/hdsapi/light/operation/message/all/page", params);
  },
  // 批量标记消息已读
  batchMarkReadMessage: (params) => {
    return post("/hdsapi/light/operation/message/batch-mark-read", params, 1);
  },
  // 新增消息
  createMessage: (params) => {
    return post("/hdsapi/light/operation/message/create", params, 1);
  },
  // 新增消息并立即发送
  createAndSendMessage: (params) => {
    return post("/hdsapi/light/operation/message/create-and-send", params, 1);
  },
  // 根据ID获取消息详情
  getMessageDetail: (params) => {
    return get("/hdsapi/light/operation/message/detail", params);
  },
  // 标记消息已读
  markMessageRead: (params) => {
    return post("/hdsapi/light/operation/message/mark-read", params);
  },
  // 分页查询消息列表
  getMessagePage: (params) => {
    return get("/hdsapi/light/operation/message/page", params);
  },
  // 发送消息
  sendMessage: (params) => {
    return post("/hdsapi/light/operation/message/send", params, 1);
  },
  // 删除消息
  deleteMessage: (params) => {
    return post("/hdsapi/light/operation/message/delete", params);
  },
  // 编辑消息
  updateMessage: (params) => {
    return post("/hdsapi/light/operation/message/update", params, 1);
  },
  // 编辑消息
  updateAndSendMessage: (params) => {
    return post("/hdsapi/light/operation/message/update-and-send", params, 1);
  },
  uploadImage: (params) => {
    return post("/hdsapi/oss/file/upload.do", params, 5);
  },

  // 题库分类管理
  // 新增分类
  addQuestionCategory: (params) => {
    return post("/hdsapi/light/operation/question/category/add", params, 1);
  },
  // 删除分类
  deleteQuestionCategory: (params) => {
    return post("/hdsapi/light/operation/question/category/delete", params);
  },
  // 修改分类
  editQuestionCategory: (params) => {
    return post("/hdsapi/light/operation/question/category/edit", params, 1);
  },
  // 获取分类详情
  getQuestionCategory: (params) => {
    return get("/hdsapi/light/operation/question/category/get", params);
  },
  // 获取分类列表
  getQuestionCategoryList: (params) => {
    return get("/hdsapi/light/operation/question/category/list", params);
  },
  // 分页查询分类列表
  getQuestionCategoryPage: (params) => {
    return get("/hdsapi/light/operation/question/category/page", params);
  },

  // 题库管理
  // 新增题库
  addQuestionBank: (params) => {
    return post("/hdsapi/light/operation/question/bank/add", params, 1);
  },
  // 删除题库
  deleteQuestionBank: (params) => {
    return post("/hdsapi/light/operation/question/bank/delete", params);
  },
  // 修改题库
  editQuestionBank: (params) => {
    return post("/hdsapi/light/operation/question/bank/edit", params, 1);
  },
  // 获取题库详情
  getQuestionBank: (params) => {
    return get("/hdsapi/light/operation/question/bank/get", params);
  },
  // 根据分类查询题库列表
  getQuestionBankListByCategory: (params) => {
    return get("/hdsapi/light/operation/question/bank/listByCategory", params);
  },
  // 分页查询题库列表
  getQuestionBankPage: (params) => {
    return get("/hdsapi/light/operation/question/bank/page", params);
  },

  // 题目管理
  // 新增题目
  addQuestion: (params) => {
    return post("/hdsapi/light/operation/question/add", params, 1);
  },
  // 删除题目
  deleteQuestion: (params) => {
    return post("/hdsapi/light/operation/question/delete", params);
  },
  // 修改题目
  editQuestion: (params) => {
    return post("/hdsapi/light/operation/question/edit", params, 1);
  },
  // 获取题目详情
  getQuestion: (params) => {
    return get("/hdsapi/light/operation/question/get", params);
  },
  // 根据题库查询题目列表
  getQuestionListByBank: (params) => {
    return get("/hdsapi/light/operation/question/listByBank", params);
  },
  // 分页查询题目列表
  getQuestionPage: (params) => {
    return get("/hdsapi/light/operation/question/page", params);
  },

  // 考试管理
  // 新增考试
  addExam: (params) => {
    return post("/hdsapi/light/operation/exam/add", params, 1);
  },
  // 删除考试
  deleteExam: (params) => {
    return post("/hdsapi/light/operation/exam/delete", params);
  },
  // 修改考试
  editExam: (params) => {
    return post("/hdsapi/light/operation/exam/edit", params, 1);
  },
  // 结束考试
  endExam: (params) => {
    return post("/hdsapi/light/operation/exam/end", params, 1);
  },
  // 获取考试详情
  getExam: (params) => {
    return get("/hdsapi/light/operation/exam/get", params);
  },
  // 获取考试试卷
  getExamPaper: (params) => {
    return get("/hdsapi/light/operation/exam/get-exam-paper", params);
  },
  // 分页查询考试列表
  getExamPage: (params) => {
    return get("/hdsapi/light/operation/exam/page", params);
  },
  // 发布考试
  publishExam: (params) => {
    return post("/hdsapi/light/operation/exam/publish", params);
  },
  // 分页查询当前用户考试列表
  getCurrentUserExamPage: (params) => {
    return post("/hdsapi/light/operation/exam/user-page", params, 1);
  },

  // 考试答卷管理
  // 根据考试ID和用户ID获取答卷
  getExamAnswerByExamAndUser: (params) => {
    return get("/hdsapi/light/operation/exam/answer/getByExamAndUser", params);
  },
  // 获取答卷详情
  getExamAnswerDetail: (params) => {
    return get("/hdsapi/light/operation/exam/answer/getDetail", params);
  },
  // 获取或创建答卷
  getOrCreateExamAnswer: (params) => {
    return get("/hdsapi/light/operation/exam/answer/getOrCreate", params);
  },
  // 根据考试ID获取所有答卷
  listExamAnswerByExam: (params) => {
    return get("/hdsapi/light/operation/exam/answer/listByExam", params);
  },
  // 提交答案
  submitExamAnswer: (params) => {
    return post("/hdsapi/light/operation/exam/answer/submit", params, 1);
  },
  // 更新考试进度
  updateExamAnswer: (params) => {
    return post("/hdsapi/light/operation/exam/answer/updateProgress", params, 1);
  },
  
  // 智能客服聊天
  // 获取聊天历史记录
  getChatHistory: (params) => {
    return get("/hdsapi/light/operation/ai/chat/history", params);
  },
  // 清除聊天历史
  clearChatHistory: (params) => {
    return post("/hdsapi/light/operation/ai/chat/history/clear", params);
  },
  // 分页获取聊天历史
  getChatHistoryByPage: (params) => {
    return get("/hdsapi/light/operation/ai/chat/history/page", params);
  },
  // 发送AI聊天消息
  sendAIChatMessage: (params) => {
    return post("/hdsapi/light/operation/ai/chat/message", params);
  },
  // 获取问题模板
  getQuestionTemplates: (params) => {
    return get("/hdsapi/light/operation/ai/chat/question-templates", params);
  },
  // 重新生成回答
  regenerateAnswer: (params) => {
    return post("/hdsapi/light/operation/ai/chat/regenerate", params);
  },
  // 流式发送消息
  sendMessageStream: (params) => {
    return post("/hdsapi/light/operation/ai/chat/stream", params);
  },

}
