<script setup>
import { ref, nextTick, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowRight } from '@element-plus/icons-vue'
import maintainanceAPI from '@/api/maintainance'

const router = useRouter()

const scrollContainer = ref()

const dataList = ref([])
const isLoadingAnswer = ref(false)
const currentMessage = ref('')
const conversationId = ref(undefined)
const firstLoaded = ref(false)
const pageSize = ref(20)

async function queryChatHistory(page, size) {
  try {
    const res = await maintainanceAPI.getChatHistoryByPage({
      pageNum: page,
      pageSize: size,
    })

    if (res && res.result && res.result.content) {
      const newMessages = res.result.content.reverse()
      if (page === 1) {
        dataList.value = newMessages
      } else {
        dataList.value = [...newMessages, ...dataList.value]
      }
      // hasMore.value = res.result.content.length === size
    }

    if (!firstLoaded.value) {
      firstLoaded.value = true
      await fetchChatTemplates()
    }
  } catch (error) {
    console.error('Failed to fetch chat history:', error)
  }
}

async function fetchChatTemplates() {
  try {
    const res = await maintainanceAPI.getQuestionTemplates()
    if (res && res.result && res.result.length > 0) {
      dataList.value.push({
        id: `templates-${Date.now()}`,
        messageType: 'template',
        templates: res.result,
        timestamp: Date.now()
      })
    }
  } catch (error) {
    console.error('Failed to fetch chat templates:', error)
  }
}

// 流式响应处理函数
async function handleStreamResponse(text, messageObj) {
  try {
    // 使用maintainanceAPI的流式接口
    const response = await maintainanceAPI.sendMessageStream({
      message: text,
      conversationId: conversationId.value,
    })

    // 如果返回的是流式数据，需要特殊处理
    if (response && response.result) {
      // 模拟流式响应效果
      const fullText = response.result.answer || response.result.content || ''
      if (fullText) {
        messageObj.aiMessage = ''
        let index = 0
        const typeWriter = () => {
          if (index < fullText.length) {
            messageObj.aiMessage += fullText.charAt(index)
            index++
            setTimeout(typeWriter, 30) // 30ms间隔模拟打字效果
          } else {
            messageObj.isAnswering = false
            isLoadingAnswer.value = false
          }
        }
        typeWriter()
      }

      if (response.result.conversation_id) {
        conversationId.value = response.result.conversation_id
      }
    } else {
      throw new Error('Invalid response format')
    }
  } catch (error) {
    console.error('Stream error:', error)
    messageObj.aiMessage = '抱歉，服务出错了，请稍后再试。'
    messageObj.isAnswering = false
    isLoadingAnswer.value = false
  } finally {
    scrollToBottom()
  }
}

async function handleSendMessage(messageText) {
  const text = messageText || currentMessage.value
  if (!text.trim()) return

  const question = {
    id: `user-${Date.now()}`,
    messageType: null,
    userMessage: text,
    templates: [],
    isAnswering: true,
    aiMessage: '',
    timestamp: Date.now()
  }

  dataList.value.push(question)
  currentMessage.value = ''

  nextTick(() => {
    scrollToBottom()
  })

  isLoadingAnswer.value = true

  // 使用流式响应
  await handleStreamResponse(text, question)
}

function handleQuestionClick(question) {
  handleSendMessage(question)
}

// 重新生成回答
async function handleRegenerateAnswer(messageId) {
  const messageIndex = dataList.value.findIndex(m => m.id === messageId)
  if (messageIndex === -1) return

  const message = dataList.value[messageIndex]
  if (!message.userMessage) return

  message.isAnswering = true
  message.aiMessage = ''

  try {
    const response = await maintainanceAPI.regenerateAnswer({
      message: message.userMessage,
      conversationId: conversationId.value,
    })

    if (response && response.data && response.data.result && response.data.result.answer) {
      message.aiMessage = response.data.result.answer
    }
  } catch (error) {
    console.error('Failed to regenerate answer:', error)
    message.aiMessage = '抱歉，重新生成回答失败，请稍后再试。'
  } finally {
    message.isAnswering = false
  }
}

async function handleRefreshTemplates() {
  const templateIndex = dataList.value.findIndex(m => m.messageType === 'template')
  if (templateIndex !== -1) {
    dataList.value.splice(templateIndex, 1)
  }
  await fetchChatTemplates()
}

function handleClose() {
  router.back()
}

// 滚动到底部
function scrollToBottom() {
  nextTick(() => {
    if (scrollContainer.value) {
      scrollContainer.value.scrollTop = scrollContainer.value.scrollHeight
    }
  })
}

// 初始化
onMounted(() => {
  queryChatHistory(1, pageSize.value)
})
</script>

<template>
  <div class="service-page">
    <div class="service-page__header">
      <div class="header-title">
        <span class="main-title">光小智</span>
        <span class="sub-title">海尔新能源AI客服</span>
      </div>
      <div class="close-button" @click="handleClose">
        <span class="close-icon">✕</span>
      </div>
    </div>

    <div class="service-page__content" ref="scrollContainer">
      <div
        v-for="message in dataList"
        :key="message.id"
        class="chat-message"
        :class="[`chat-message--${message.messageType}`]"
      >
        <div
          v-if="message.messageType === null && message.userMessage"
          class="message-bubble message-bubble--user"
        >
          <span class="message-text">{{ message.userMessage }}</span>
        </div>
        <div
          v-if="message.messageType === null && (message.aiMessage || message.isAnswering)"
          class="message-bubble message-bubble--assistant"
        >
          <div class="message-text">
            <div v-if="message.isAnswering" class="typing-dots">
              <div class="dot" />
              <div class="dot" />
              <div class="dot" />
            </div>
            <span v-else>
              {{ message.aiMessage }}
            </span>
          </div>
          <div v-if="message.aiMessage && !message.isAnswering" class="message-actions">
            <div class="action-button retry-button" @click="handleRegenerateAnswer(message.id)">
              <span class="action-icon">↺</span>
              <span class="action-text">重新回答</span>
            </div>
          </div>
        </div>
        <div v-if="message.messageType === 'template'" class="template-container">
          <div class="prompt-section">
            <span class="prompt-title">Hi，试着问我</span>
            <div class="question-list">
              <div
                v-for="template in message.templates"
                :key="template.id"
                class="question-item"
                @click="handleQuestionClick(template.question)"
              >
                <span class="question-text">{{ template.question }}</span>
                <div class="send-icon-container">
                  <el-icon class="send-icon"><ArrowRight /></el-icon>
                </div>
              </div>
            </div>
          </div>

          <div class="refresh-section">
            <div class="refresh-button" @click="handleRefreshTemplates">
              <span class="refresh-icon">↻</span>
              <span class="refresh-text">换一换</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="service-page__footer">
      <div class="input-area">
        <el-input
          v-model="currentMessage"
          type="text"
          placeholder="有问题尽管问我"
          class="input-field"
          @keyup.enter="handleSendMessage()"
        />
        <el-button
          type="primary"
          class="send-button-main"
          @click="handleSendMessage()"
          :loading="isLoadingAnswer"
        >
          发送
        </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
@keyframes typing-animation {
  0% {
    transform: translateY(0);
    opacity: 0.3;
  }

  20% {
    transform: translateY(-4px);
    opacity: 1;
  }

  40%,
  100% {
    transform: translateY(0);
    opacity: 0.3;
  }
}

.service-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(147deg, #e3edff, #fafcff 41%);
  overflow: hidden;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    position: relative;
    z-index: 10;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);

    .header-title {
      display: flex;
      flex-direction: column;
    }

    .main-title {
      font-family: 'PingFang SC', sans-serif;
      font-size: 20px;
      color: #3d3d3d;
      font-weight: 500;
    }

    .sub-title {
      font-family: 'PingFang SC', sans-serif;
      font-size: 12px;
      color: #666;
    }

    .close-button {
      padding: 8px;
      cursor: pointer;
      border-radius: 4px;
      transition: background-color 0.2s;

      &:hover {
        background-color: rgba(0, 0, 0, 0.1);
      }
    }

    .close-icon {
      font-size: 18px;
      color: #333333;
    }
  }

  &__content {
    flex: 1;
    overflow-y: auto;
    padding: 0 20px;
    box-sizing: border-box;
  }

  .prompt-section {
    background-color: #ffffff;
    border-radius: 18px;
    padding: 17px 20px;
    margin-top: 10px;
    box-shadow: 0px 0px 8px 0px rgba(64, 150, 254, 0.03);
  }

  .prompt-title {
    font-family: 'PingFang SC', sans-serif;
    font-size: 20px;
    color: #3d3d3d;
    font-weight: bold;
    display: block;
    margin-bottom: 12px;
  }

  .question-list {
    display: flex;
    flex-direction: column;
  }

  .question-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f2f2f2;

    &:last-child {
      border-bottom: none;
    }
  }

  .question-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    color: #3d3d3d;
    flex: 1;
  }

  .send-icon-container {
    background-color: #eaf3ff;
    border-radius: 4px;
    width: 27px;
    height: 27px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 10px;
  }

  .send-icon {
    width: 18px;
    height: 18px;
  }

  .refresh-section {
    display: flex;
    justify-content: flex-start;
    margin-top: 15px;
    margin-bottom: 15px;
  }

  .refresh-button {
    display: flex;
    align-items: center;
    background-color: #ffffff;
    padding: 3px 8px;
    border-radius: 9px;
    box-shadow: 0px 0px 8px 0px rgba(64, 150, 254, 0.03);
  }

  .refresh-icon {
    font-size: 14px;
    color: #a8abb2;
    margin-right: 4px;
  }

  .refresh-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 11px;
    color: #a8abb2;
  }

  .template-container {
    width: 100%;
  }

  .action-button {
    display: flex;
    align-items: center;
    padding: 3px 8px;
    background-color: #ffffff;
    border-radius: 9px;
    box-shadow: 0px 0px 8px 0px rgba(64, 150, 254, 0.03);
  }

  .action-icon {
    font-size: 14px;
    color: #19385d;
  }

  .action-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 11px;
    color: #19385d;
    margin-left: 4px;
  }

  .feedback-buttons {
    .action-icon {
      padding: 0 5px;
    }
    .separator {
      width: 1px;
      height: 14px;
      background-color: #d8d8d8;
      margin: 0 5px;
    }
  }

  .chat-message {
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-bottom: 16px;
    gap: 12px;
  }

  .message-bubble {
    padding: 12px 16px;
    border-radius: 18px;
    max-width: 70%;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    word-wrap: break-word;

    &--user {
      background: linear-gradient(106deg, rgba(64, 150, 254, 0.9), rgba(64, 150, 254, 0.7) 97%);
      color: #ffffff;
      border-radius: 18px 18px 4px 18px;
      align-self: flex-end;

      .message-text {
        line-height: 1.4;
        font-weight: 400;
      }
    }

    &--assistant {
      background-color: #ffffff;
      color: #333;
      border-radius: 18px 18px 18px 4px;
      align-self: flex-start;
      border: 1px solid #f0f0f0;
    }
  }

  .typing-dots {
    display: flex;
    align-items: center;
    padding-top: 8px;

    .dot {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: #a8abb2;
      margin: 0 2px;
      animation: typing-animation 1.4s infinite both;

      &:nth-child(2) {
        animation-delay: 0.2s;
      }

      &:nth-child(3) {
        animation-delay: 0.4s;
      }
    }
  }

  .message-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  .message-actions {
    display: flex;
    justify-content: flex-start;
    gap: 10px;
    align-items: center;
    margin-top: 10px;
    padding-top: 8px;
    border-top: 1px solid #eaeaea;

    .action-button {
      padding: 2px 6px;
      background-color: transparent;
      box-shadow: none;
    }

    .action-icon {
      font-size: 13px;
    }

    .action-text {
      font-size: 10px;
    }
    .feedback-buttons .separator {
      background-color: #d1d1d1;
    }
  }

  &__footer {
    padding: 16px 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-top: 1px solid #f0f0f0;
    position: relative;
    z-index: 10;
  }

  .input-area {
    display: flex;
    align-items: center;
    gap: 12px;

    .input-field {
      flex: 1;

      :deep(.el-input__wrapper) {
        border-radius: 20px;
        padding: 8px 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }

    .send-button-main {
      border-radius: 20px;
      padding: 8px 20px;
    }
  }
}
</style>
