<script setup>
import { ref, nextTick, onMounted } from 'vue'
import { ArrowRight } from '@element-plus/icons-vue'
import maintainanceAPI from '@/api/maintainance'

const scrollContainer = ref()

const dataList = ref([])
const isLoadingAnswer = ref(false)
const currentMessage = ref('')
const conversationId = ref(undefined)
const firstLoaded = ref(false)
const pageSize = ref(20)

async function queryChatHistory(page, size) {
  try {
    const res = await maintainanceAPI.getChatHistoryByPage({
      pageNum: page,
      pageSize: size,
    })

    if (res && res.result && res.result.content) {
      const newMessages = res.result.content.reverse()
      if (page === 1) {
        dataList.value = newMessages
      } else {
        dataList.value = [...newMessages, ...dataList.value]
      }
      // hasMore.value = res.result.content.length === size
    }

    if (!firstLoaded.value) {
      firstLoaded.value = true
      await fetchChatTemplates()
    }
  } catch (error) {
    console.error('Failed to fetch chat history:', error)
  }
}

async function fetchChatTemplates() {
  try {
    const res = await maintainanceAPI.getQuestionTemplates()
    if (res && res.result && res.result.length > 0) {
      dataList.value.push({
        id: `templates-${Date.now()}`,
        messageType: 'template',
        templates: res.result,
        timestamp: Date.now()
      })
    }
  } catch (error) {
    console.error('Failed to fetch chat templates:', error)
  }
}

// 流式响应处理函数
async function handleStreamResponse(text, messageObj) {
  try {
    console.log('Starting stream response for:', text)

    const token = JSON.parse(localStorage.getItem('logins'))?.access_token
    const baseURL = import.meta.env.VITE_APP_BASE_API || ''

    const response = await fetch(`${baseURL}/hdsapi/light/operation/ai/chat/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        message: text,
        conversationId: conversationId.value,
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    let buffer = ''

    messageObj.aiMessage = ''

    // eslint-disable-next-line no-constant-condition
    while (true) {
      const { done, value } = await reader.read()
      if (done) {
        console.log('Stream reading completed')
        isLoadingAnswer.value = false
        break
      }

      const chunk = decoder.decode(value, { stream: true })
      console.log('Received chunk:', chunk)

      buffer += chunk
      const lines = buffer.split('\n')
      buffer = lines.pop() || ''

      console.log('Split into lines:', lines)

      for (const line of lines) {
        console.log('Processing line:', line)

        if (line.trim()) {
          console.log('Processing line:', line)

          // 处理以 "data:" 开头的行，可能有多个data:前缀
          if (line.startsWith('data:')) {
            let jsonStr = line
            // 移除所有的 "data:" 前缀
            while (jsonStr.startsWith('data:')) {
              jsonStr = jsonStr.slice(5).trim()
            }
            console.log('Extracted JSON string:', jsonStr)

            if (jsonStr === '[DONE]') {
              console.log('Received [DONE] signal')
              isLoadingAnswer.value = false
              break
            }

            if (jsonStr) {
              try {
                const data = JSON.parse(jsonStr)
                console.log('Parsed stream data:', data)
                console.log('Event type:', data.event)

                // 尝试多种可能的数据结构
                let content = ''
                if (data.content) {
                  content = data.content
                } else if (data.delta?.content) {
                  content = data.delta.content
                } else if (data.choices?.[0]?.delta?.content) {
                  content = data.choices[0].delta.content
                } else if (data.message) {
                  content = data.message
                } else if (data.text) {
                  content = data.text
                } else if (data.answer) {
                  content = data.answer
                }

                if (content) {
                  // 直接更新dataList中的对象，确保响应式
                  const messageIndex = dataList.value.findIndex(m => m.id === messageObj.id)
                  if (messageIndex !== -1) {
                    dataList.value[messageIndex].aiMessage += content
                    console.log('Updated aiMessage:', dataList.value[messageIndex].aiMessage)
                  }

                  // 强制触发Vue响应式更新
                  nextTick(() => {
                    scrollToBottom()
                  })
                }

                if (data.conversation_id) {
                  conversationId.value = data.conversation_id
                }
              } catch (e) {
                console.error('Error parsing JSON:', e, 'String:', jsonStr)
              }
            }
          } else {
            console.log('Non-data line:', line)
          }
        }
      }
    }
  } catch (error) {
    console.error('Stream error:', error)
    messageObj.aiMessage = '抱歉，服务出错了，请稍后再试。'
  } finally {
    isLoadingAnswer.value = false
    scrollToBottom()
  }
}

async function handleSendMessage(messageText) {
  const text = messageText || currentMessage.value
  if (!text.trim()) return

  const question = {
    id: `user-${Date.now()}`,
    messageType: null,
    userMessage: text,
    templates: [],
    aiMessage: '',
    timestamp: Date.now()
  }

  dataList.value.push(question)

  currentMessage.value = ''

  nextTick(() => {
    scrollToBottom()
  })

  isLoadingAnswer.value = true

  // 使用流式响应
  await handleStreamResponse(text, question)
}

function handleQuestionClick(question) {
  handleSendMessage(question)
}

// 重新生成回答的流式处理函数
async function handleRegenerateStreamResponse(messageId, messageObj) {
  try {
    console.log('Starting regenerate stream response for messageId:', messageId)

    const token = JSON.parse(localStorage.getItem('logins'))?.access_token
    const baseURL = import.meta.env.VITE_APP_BASE_API || ''

    // 使用URLSearchParams构建form数据
    const formData = new URLSearchParams()
    formData.append('messageId', messageId)

    const response = await fetch(`${baseURL}/hdsapi/light/operation/ai/chat/regenerate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Bearer ${token}`
      },
      body: formData
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    let buffer = ''

    messageObj.aiMessage = ''

    // eslint-disable-next-line no-constant-condition
    while (true) {
      const { done, value } = await reader.read()
      if (done) {
        console.log('Regenerate stream reading completed')
        isLoadingAnswer.value = false
        break
      }

      const chunk = decoder.decode(value, { stream: true })
      console.log('Received regenerate chunk:', chunk)

      buffer += chunk
      const lines = buffer.split('\n')
      buffer = lines.pop() || ''

      console.log('Split into lines:', lines)

      for (const line of lines) {
        if (line.trim()) {
          console.log('Processing regenerate line:', line)

          // 处理以 "data:" 开头的行，可能有多个data:前缀
          if (line.startsWith('data:')) {
            let jsonStr = line
            // 移除所有的 "data:" 前缀
            while (jsonStr.startsWith('data:')) {
              jsonStr = jsonStr.slice(5).trim()
            }
            console.log('Extracted regenerate JSON string:', jsonStr)

            if (jsonStr === '[DONE]') {
              console.log('Received regenerate [DONE] signal')
              isLoadingAnswer.value = false
              break
            }

            if (jsonStr) {
              try {
                const data = JSON.parse(jsonStr)
                console.log('Parsed regenerate stream data:', data)
                console.log('Event type:', data.event)

                // 尝试多种可能的数据结构
                let content = ''
                if (data.content) {
                  content = data.content
                } else if (data.delta?.content) {
                  content = data.delta.content
                } else if (data.choices?.[0]?.delta?.content) {
                  content = data.choices[0].delta.content
                } else if (data.message) {
                  content = data.message
                } else if (data.text) {
                  content = data.text
                } else if (data.answer) {
                  content = data.answer
                }

                if (content) {
                  // 直接更新dataList中的对象，确保响应式
                  const messageIndex = dataList.value.findIndex(m => m.id === messageObj.id)
                  if (messageIndex !== -1) {
                    dataList.value[messageIndex].aiMessage += content
                    console.log('Updated regenerate aiMessage:', dataList.value[messageIndex].aiMessage)
                  }

                  // 强制触发Vue响应式更新
                  nextTick(() => {
                    scrollToBottom()
                  })
                }

                if (data.conversation_id) {
                  conversationId.value = data.conversation_id
                }
              } catch (e) {
                console.error('Error parsing regenerate JSON:', e, 'String:', jsonStr)
              }
            }
          } else {
            console.log('Non-data regenerate line:', line)
          }
        }
      }
    }
  } catch (error) {
    console.error('Regenerate stream error:', error)
    messageObj.aiMessage = '抱歉，重新生成回答失败，请稍后再试。'
  } finally {
    isLoadingAnswer.value = false
    scrollToBottom()
  }
}

// 重新生成回答 - 使用流式传输
async function handleRegenerateAnswer(messageId) {
  const messageIndex = dataList.value.findIndex(m => m.id === messageId)
  if (messageIndex === -1) return

  const message = dataList.value[messageIndex]
  if (!message.userMessage) return

  isLoadingAnswer.value = true
  message.aiMessage = ''

  // 使用重新生成的流式响应，传递messageId
  await handleRegenerateStreamResponse(messageId, message)
}

async function handleRefreshTemplates() {
  const templateIndex = dataList.value.findIndex(m => m.messageType === 'template')
  if (templateIndex !== -1) {
    dataList.value.splice(templateIndex, 1)
  }
  await fetchChatTemplates()
}

// 滚动到底部
function scrollToBottom() {
  nextTick(() => {
    if (scrollContainer.value) {
      scrollContainer.value.scrollTop = scrollContainer.value.scrollHeight
    }
  })
}

// 初始化
onMounted(() => {
  queryChatHistory(1, pageSize.value)
})
</script>

<template>
  <div class="service-page">
    <div class="service-page__header">
      <div class="header-title">
        <span class="main-title">光小智</span>
        <span class="sub-title">海尔新能源AI客服</span>
      </div>
    </div>

    <div class="service-page__content" ref="scrollContainer">
      <div
        v-for="message in dataList"
        :key="message.id"
        class="chat-message"
        :class="[`chat-message--${message.messageType}`]"
      >
        <div
          v-if="message.messageType === null && message.userMessage"
          class="message-bubble message-bubble--user"
        >
          <span class="message-text">{{ message.userMessage }}</span>
        </div>
        <div
          v-if="message.messageType === null && (message.aiMessage || isLoadingAnswer)"
          class="message-bubble message-bubble--assistant"
        >
          <div class="message-text">
            <span v-if="message.aiMessage">{{ message.aiMessage }}</span>
            <div v-if="isLoadingAnswer && !message.aiMessage" class="typing-dots">
              <div class="dot" />
              <div class="dot" />
              <div class="dot" />
            </div>
          </div>
          <div v-if="message.aiMessage && !isLoadingAnswer" class="message-actions">
            <div class="action-button retry-button" @click="handleRegenerateAnswer(message.id)">
              <span class="action-icon">↺</span>
              <span class="action-text">重新回答</span>
            </div>
          </div>
        </div>
        <div v-if="message.messageType === 'template'" class="template-container">
          <div class="prompt-section">
            <span class="prompt-title">Hi，试着问我</span>
            <div class="question-list">
              <div
                v-for="template in message.templates"
                :key="template.id"
                class="question-item"
                @click="handleQuestionClick(template.question)"
              >
                <span class="question-text">{{ template.question }}</span>
                <div class="send-icon-container">
                  <el-icon class="send-icon"><ArrowRight /></el-icon>
                </div>
              </div>
            </div>
          </div>

          <div class="refresh-section">
            <div class="refresh-button" @click="handleRefreshTemplates">
              <span class="refresh-icon">↻</span>
              <span class="refresh-text">换一换</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="service-page__footer">
      <div class="input-area">
        <el-input
          v-model="currentMessage"
          type="text"
          placeholder="有问题尽管问我"
          class="input-field"
          @keyup.enter="handleSendMessage()"
        />
        <el-button
          type="primary"
          class="send-button-main"
          @click="handleSendMessage()"
          :loading="isLoadingAnswer"
        >
          发送
        </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
@keyframes typing-animation {
  0% {
    transform: translateY(0);
    opacity: 0.3;
  }

  20% {
    transform: translateY(-4px);
    opacity: 1;
  }

  40%,
  100% {
    transform: translateY(0);
    opacity: 0.3;
  }
}

.service-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(147deg, #e3edff, #fafcff 41%);
  overflow: hidden;

  &__header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 12px 20px;
    position: relative;
    z-index: 10;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);

    .header-title {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .main-title {
      font-family: 'PingFang SC', sans-serif;
      font-size: 20px;
      color: #3d3d3d;
      font-weight: 500;
    }

    .sub-title {
      font-family: 'PingFang SC', sans-serif;
      font-size: 12px;
      color: #666;
    }
  }

  &__content {
    flex: 1;
    overflow-y: auto;
    padding: 0 20px;
    box-sizing: border-box;
  }

  .prompt-section {
    background-color: #ffffff;
    border-radius: 18px;
    padding: 17px 20px;
    margin-top: 10px;
    box-shadow: 0px 0px 8px 0px rgba(64, 150, 254, 0.03);
  }

  .prompt-title {
    font-family: 'PingFang SC', sans-serif;
    font-size: 20px;
    color: #3d3d3d;
    font-weight: bold;
    display: block;
    margin-bottom: 12px;
  }

  .question-list {
    display: flex;
    flex-direction: column;
  }

  .question-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f2f2f2;

    &:last-child {
      border-bottom: none;
    }
  }

  .question-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    color: #3d3d3d;
    flex: 1;
  }

  .send-icon-container {
    background-color: #eaf3ff;
    border-radius: 4px;
    width: 27px;
    height: 27px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 10px;
  }

  .send-icon {
    width: 18px;
    height: 18px;
  }

  .refresh-section {
    display: flex;
    justify-content: flex-start;
    margin-top: 15px;
    margin-bottom: 15px;
  }

  .refresh-button {
    display: flex;
    align-items: center;
    background-color: #ffffff;
    padding: 3px 8px;
    border-radius: 9px;
    box-shadow: 0px 0px 8px 0px rgba(64, 150, 254, 0.03);
  }

  .refresh-icon {
    font-size: 14px;
    color: #a8abb2;
    margin-right: 4px;
  }

  .refresh-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 11px;
    color: #a8abb2;
  }

  .template-container {
    width: 100%;
  }

  .action-button {
    display: flex;
    align-items: center;
    padding: 3px 8px;
    background-color: #ffffff;
    border-radius: 9px;
    box-shadow: 0px 0px 8px 0px rgba(64, 150, 254, 0.03);
  }

  .action-icon {
    font-size: 14px;
    color: #19385d;
  }

  .action-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 11px;
    color: #19385d;
    margin-left: 4px;
  }

  .feedback-buttons {
    .action-icon {
      padding: 0 5px;
    }
    .separator {
      width: 1px;
      height: 14px;
      background-color: #d8d8d8;
      margin: 0 5px;
    }
  }

  .chat-message {
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-bottom: 16px;
    gap: 12px;
  }

  .message-bubble {
    padding: 12px 16px;
    border-radius: 18px;
    max-width: 70%;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    word-wrap: break-word;

    &--user {
      background: linear-gradient(106deg, rgba(64, 150, 254, 0.9), rgba(64, 150, 254, 0.7) 97%);
      color: #ffffff;
      border-radius: 18px 18px 4px 18px;
      align-self: flex-end;

      .message-text {
        line-height: 1.4;
        font-weight: 400;
      }
    }

    &--assistant {
      background-color: #ffffff;
      color: #333;
      border-radius: 18px 18px 18px 4px;
      align-self: flex-start;
      border: 1px solid #f0f0f0;
    }
  }

  .typing-dots {
    display: flex;
    align-items: center;
    padding-top: 8px;

    .dot {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: #a8abb2;
      margin: 0 2px;
      animation: typing-animation 1.4s infinite both;

      &:nth-child(2) {
        animation-delay: 0.2s;
      }

      &:nth-child(3) {
        animation-delay: 0.4s;
      }
    }
  }

  .message-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  .message-actions {
    display: flex;
    justify-content: flex-start;
    gap: 10px;
    align-items: center;
    margin-top: 10px;
    padding-top: 8px;
    border-top: 1px solid #eaeaea;

    .action-button {
      padding: 2px 6px;
      background-color: transparent;
      box-shadow: none;
    }

    .action-icon {
      font-size: 13px;
    }

    .action-text {
      font-size: 10px;
    }
    .feedback-buttons .separator {
      background-color: #d1d1d1;
    }
  }

  &__footer {
    padding: 16px 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-top: 1px solid #f0f0f0;
    position: relative;
    z-index: 10;
  }

  .input-area {
    display: flex;
    align-items: center;
    gap: 12px;

    .input-field {
      flex: 1;

      :deep(.el-input__wrapper) {
        border-radius: 20px;
        padding: 8px 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }

    .send-button-main {
      border-radius: 20px;
      padding: 8px 20px;
    }
  }
}
</style>
