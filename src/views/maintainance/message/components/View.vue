<template>
    <el-drawer
        title="查看消息"
        v-model="dialogVisible"
        size="650px"
        :close-on-click-modal="false"
        @close="handleClose"
        direction="rtl"
    >
        <div class="article-view">
            <h1 class="article-title">{{ messageData.subject || '无标题' }}</h1>
            <!-- <div class="article-meta">
                <span>类型: {{ getMessageTypeLabel(messageData.messageType) }}</span>
                <span>状态: <el-tag :type="getStatusType(messageData.status)" size="small">{{ getStatusLabel(messageData.status) }}</el-tag></span>
                <span>发送时间: {{ messageData.sendTime ? messageData.sendTime.replace('T', ' ') : '-' }}</span>
            </div>
            <div class="article-meta" v-if="roleNames.length > 0 || subCenterNames.length > 0">
                 <span v-if="roleNames.length > 0">发送对象 (角色): {{ roleNames.join(', ') }}</span>
                 <span v-if="subCenterNames.length > 0">发送对象 (分中心): {{ subCenterNames.join(', ') }}</span>
            </div> -->
            <div class="article-meta">
                <span>创建人: {{ messageData.createdByName || '-' }}</span>
                <span>创建时间: {{ messageData.createdAt ? messageData.createdAt.replace('T', ' ') : '-' }}</span>
            </div>

            <el-divider />

            <div class="article-content" v-html="messageData.content || '无内容'"></div>
        </div>

        <div class="drawer-footer">
            <el-button @click="handleClose">关 闭</el-button>
        </div>
    </el-drawer>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { useDictStore } from '@/stores/modules/dict';
import _D from '@/edata/_osp_data';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible']);

const dialogVisible = ref(false);
const messageData = ref({});
const dictStore = useDictStore();

const roleOptions = computed(() => dictStore.getDictByType('user_role'));
const subCenterOptions = _D.subCenterList;

watch(() => props.visible, (val) => {
  dialogVisible.value = val;
  if (val) {
    messageData.value = { ...props.data };
  }
});

watch(() => props.data, (newData) => {
  if (props.visible) {
    messageData.value = { ...newData };
  }
}, { deep: true });


const getMessageTypeLabel = (type) => {
  const typeMap = {
    'SYSTEM': '系统消息',
    'BUSINESS': '业务消息'
  };
  return typeMap[type] || type || '-';
};

const getStatusLabel = (status) => {
  const statusMap = {
    'DRAFT': '草稿',
    'SENT': '已发送',
    'FAILED': '发送失败',
    'PENDING': '待发送'
  };
  return statusMap[status] || status || '-';
};

const getStatusType = (status) => {
  const typeMap = {
    'DRAFT': 'info',
    'SENT': 'success',
    'FAILED': 'danger',
    'PENDING': 'warning'
  };
  return typeMap[status] || '';
};

const roleNames = computed(() => {
  if (!messageData.value.roleCodes || !roleOptions.value) {
    return [];
  }
  return messageData.value.roleCodes.map(code => {
    const role = roleOptions.value.find(r => r.value === code);
    return role ? role.label : code;
  });
});

const subCenterNames = computed(() => {
  if (!messageData.value.subCenterCodes || !subCenterOptions) {
    return [];
  }
  return messageData.value.subCenterCodes.map(code => {
    const center = subCenterOptions.find(c => c.value === code);
    return center ? center.label : code;
  });
});

const handleClose = () => {
  emit('update:visible', false);
};
</script>

<style lang="less" scoped>
.article-view {
  padding: 0 10px;
  .article-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #303133;
    text-align: center;
  }

  .article-meta {
    font-size: 13px;
    color: #606266;
    margin-bottom: 8px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 5px 15px;

    span {
      margin-right: 15px;
      &:last-child {
        margin-right: 0;
      }
    }
    .el-tag {
      margin-left: 5px;
    }
  }

  .el-divider {
    margin: 20px 0;
  }

  .article-content {
    font-size: 14px;
    line-height: 1.8;
    color: #303133;
    word-wrap: break-word;
    white-space: pre-wrap;

    :deep(p) {
      margin-bottom: 1em;
    }
    :deep(img) {
      max-width: 100%;
      display: block;
      margin: 10px auto;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
        margin-top: 1.5em;
        margin-bottom: 0.8em;
        font-weight: 600;
    }
    :deep(ul), :deep(ol) {
        padding-left: 20px;
        margin-bottom: 1em;
    }
    :deep(li) {
        margin-bottom: 0.5em;
    }
    :deep(blockquote) {
        margin: 1em 0;
        padding: 10px 15px;
        border-left: 4px solid #dfe2e5;
        background-color: #f8f9fa;
        color: #6a737d;
    }
  }
}

.drawer-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16px 20px;
    background-color: #fff;
    border-top: 1px solid #e4e7ed;
    text-align: right;

    .el-button {
        margin-left: 8px;
    }
}
</style>
