<template>
  <div class="exam-score-container" v-if="examData">
    <div class="page-header">
      <h1>{{ examData.examName }} - 考试结果</h1>
    </div>

    <div class="result-summary">
      <div class="score-circle" :class="passStatus.class">
        <div class="score">{{ examData.score }}</div>
        <div class="total-score">满分{{ examData.totalScore }}</div>
      </div>
      <div class="result-text">{{ passStatus.text }}</div>
      <div class="congrats-message">恭喜您完成本次考试，{{ passStatus.text }}！</div>
    </div>

    <div class="stats-grid">
      <div class="stat-item">
        <div class="stat-value correct">{{ correctCount }}</div>
        <div class="stat-label">答对题数</div>
      </div>
      <div class="stat-item">
        <div class="stat-value incorrect">{{ incorrectCount }}</div>
        <div class="stat-label">答错题数</div>
      </div>
      <div class="stat-item">
        <div class="stat-value duration">{{ examDuration }}</div>
        <div class="stat-label">答题用时</div>
      </div>
      <div class="stat-item">
        <div class="stat-value accuracy">{{ accuracy }}%</div>
        <div class="stat-label">正确率</div>
      </div>
    </div>

    <div class="section">
      <h2 class="section-title">答题情况</h2>
      <div class="answer-sheet">
        <div
          v-for="(question, index) in examData.questions"
          :key="question.id"
          class="answer-item"
          :class="{ 'correct': question.correct, 'incorrect': !question.correct }"
        >
          {{ index + 1 }}
        </div>
      </div>
    </div>

    <div class="section" v-if="incorrectQuestions.length > 0">
      <h2 class="section-title">错题分析</h2>
      <div class="analysis-list">
        <div v-for="(question) in incorrectQuestions" :key="question.id" class="analysis-item">
          <div class="question-title">
            <span class="question-index">{{ getQuestionIndex(question.id) }}.</span>
            {{ question.title }} ({{ question.score }}分)
          </div>
          <div class="options-list">
            <div
              v-for="option in question.options"
              :key="option.id"
              class="option-item"
              :class="getOptionClass(question, option.optionKey)"
            >
              {{ option.optionKey }}. {{ option.content }}
            </div>
          </div>
          <div class="answer-analysis">
            <div class="answer-row">
              <span class="user-answer">您的答案：{{ question.userAnswer || '未作答' }}</span>
              <span class="correct-answer">正确答案：{{ question.answer }}</span>
            </div>
            <div class="analysis-text">解析：{{ question.analysis }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="action-buttons">
      <el-button type="primary" @click="goHome">返回考试首页</el-button>
    </div>
  </div>
  <el-skeleton v-else :rows="20" animated />
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import API from '@/api/maintainance';
import { ElMessage } from 'element-plus';

const route = useRoute();
const router = useRouter();

const examData = ref(null);
const answerId = route.query.answerId || null;

const passStatus = computed(() => {
  if (!examData.value) return { text: '', class: '' };
  if (examData.value.result === 1) {
    return { text: '考试通过', class: 'pass' };
  }
  const score = examData.value.score;
  const totalScore = examData.value.totalScore;
  if (totalScore === 0) return { text: '考试完成', class: 'fail' };

  const rate = score / totalScore;
  if (rate >= 0.9) return { text: '优秀', class: 'pass' };
  if (rate >= 0.8) return { text: '良好', class: 'pass' };
  if (rate >= 0.6) return { text: '及格', class: 'pass' };

  return { text: '考试未通过', class: 'fail' };
});

const correctCount = computed(() => {
  if (!examData.value || !examData.value.questions) return 0;
  return examData.value.questions.filter(q => q.correct).length;
});

const incorrectCount = computed(() => {
  if (!examData.value || !examData.value.questions) return 0;
  return examData.value.questions.filter(q => !q.correct).length;
});

const accuracy = computed(() => {
  if (!examData.value || !examData.value.questions || examData.value.questions.length === 0) return 0;
  return ((correctCount.value / examData.value.questions.length) * 100).toFixed(0);
});

const incorrectQuestions = computed(() => {
  if (!examData.value || !examData.value.questions) return [];
  return examData.value.questions.filter(q => !q.correct);
});

const examDuration = computed(() => {
  if (!examData.value || !examData.value.startTime || !examData.value.endTime) {
    return '--';
  }
  const startTime = new Date(examData.value.startTime).getTime();
  const endTime = new Date(examData.value.endTime).getTime();
  if (isNaN(startTime) || isNaN(endTime) || endTime < startTime) {
    return '--';
  }
  const durationMinutes = Math.round((endTime - startTime) / (1000 * 60));
  return `${durationMinutes}分钟`;
});

const fetchExamResult = async () => {
  if (!answerId) {
    ElMessage.error('缺少考试记录ID');
    return;
  }
  try {
    const res = await API.getExamAnswerDetail({ answerId });
    if (res.success && res.result) {
      examData.value = res.result;
    } else {
      ElMessage.error(res.error || '获取考试结果失败');
    }
  } catch (error) {
     ElMessage.error('请求异常，请稍后再试');
  }
};

const getQuestionIndex = (questionId) => {
  if (!examData.value || !examData.value.questions) return -1;
  return examData.value.questions.findIndex(q => q.id === questionId) + 1;
};

const getOptionClass = (question, optionKey) => {
  const correctAnswer = question.answer;
  const userAnswer = question.userAnswer;
  if (correctAnswer && correctAnswer.includes(optionKey)) {
    return 'correct-option';
  }
  if (userAnswer && userAnswer.includes(optionKey) && (!correctAnswer || !correctAnswer.includes(optionKey))) {
    return 'wrong-option';
  }
  return '';
};

const goHome = () => {
  router.push('/maintainance/training/exam/my');
};

onMounted(() => {
  fetchExamResult();
});
</script>

<style lang="less" scoped>
.exam-score-container {
  padding: 24px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
  margin: 16px;

  .page-header {
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 24px;
    margin-bottom: 32px;
    h1 {
      font-size: 24px;
      font-weight: 500;
      color: #303133;
    }
  }

  .result-summary {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 40px;

    .score-circle {
      width: 160px;
      height: 160px;
      border-radius: 50%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      border-width: 12px;
      border-style: solid;

      &.pass {
        border-color: #67c23a;
      }
      &.fail {
        border-color: #f56c6c;
      }

      .score {
        font-size: 48px;
        color: #303133;
        font-weight: bold;
        line-height: 1;
      }
      .total-score {
        font-size: 16px;
        color: #606266;
        margin-top: 8px;
      }
    }

    .result-text {
      font-size: 20px;
      color: #303133;
      font-weight: bold;
      margin-top: 24px;
    }
    .congrats-message {
      font-size: 14px;
      color: #606266;
      margin-top: 8px;
    }
  }

  .stats-grid {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    margin-bottom: 40px;

    .stat-item {
      flex: 1;
      background-color: #f5f7fa;
      border-radius: 4px;
      padding: 16px;
      text-align: center;
      .stat-value {
        font-size: 32px;
        font-weight: bold;
        line-height: 1.5;
        &.correct {
          color: #67c23a;
        }
        &.incorrect {
          color: #f56c6c;
        }
        &.duration {
          color: #409eff;
        }
        &.accuracy {
          color: #303133;
        }
      }
      .stat-label {
        font-size: 16px;
        color: #606266;
        margin-top: 8px;
      }
    }
  }
  
  .section {
    margin-bottom: 24px;
    .section-title {
      font-size: 20px;
      color: #303133;
      padding-bottom: 12px;
      border-bottom: 1px solid #dcdfe6;
      margin-bottom: 20px;
    }
  }

  .answer-sheet {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    .answer-item {
      width: 40px;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px solid;
      border-radius: 4px;
      font-size: 16px;

      &.correct {
        background-color: rgba(103, 194, 58, 0.1);
        border-color: #67c23a;
        color: #303133;
      }
      &.incorrect {
        background-color: rgba(245, 108, 108, 0.1);
        border-color: #f56c6c;
        color: #303133;
      }
    }
  }

  .analysis-list {
    .analysis-item {
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      padding: 24px;
      margin-bottom: 20px;

      .question-title {
        font-size: 16px;
        color: #303133;
        margin-bottom: 16px;
        .question-index {
          color: #409eff;
          margin-right: 4px;
        }
      }

      .options-list {
        margin-bottom: 16px;
        padding-left: 20px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;

        .option-item {
          padding: 5px 8px;
          border-radius: 4px;
          width: 100%;
          color: #303133;
        }

        .correct-option {
          color: #19be6b;
          background-color: rgba(25, 190, 107, 0.1);
        }
        .wrong-option {
          color: #ed4014;
          background-color: rgba(237, 64, 20, 0.1);
        }
      }
      
      .answer-analysis {
        background-color: #ecf5ff;
        padding: 15px;
        border-radius: 4px;
        font-size: 14px;
        .answer-row {
          margin-bottom: 8px;
          display: flex;
          gap: 24px;
          .user-answer {
            color: #303133;
          }
          .correct-answer {
            color: #67C23A;
          }
        }
        .analysis-text {
          color: #606266;
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 24px;
    margin-top: 40px;
  }
}
</style>
