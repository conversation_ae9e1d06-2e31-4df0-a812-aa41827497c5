<template>
	<div style="height: 100%;">
		<el-tabs v-model="activeTab">
			<el-tab-pane label="户用备案" name="HOUSEHOLD"></el-tab-pane>
			<el-tab-pane label="公司备案" name="COMPANY"></el-tab-pane>
		</el-tabs>
		<div class="cus-container">
			<div style="display: flex;flex-direction: column;gap: 12px;flex: 1;width: 100%;">
				<div class="statistic-container">
					<div class="statistic-item">
						<div class="statistic-row">
							<span>上期用电量总和</span>
							<span>{{ statisticTotal?.lastPeriodElec ?? '-' }}kWh</span>
						</div>
						<div class="statistic-row">
							<span>用电量总和</span>
							<span>{{ statisticTotal?.totalElec ?? '-' }}kWh</span>
						</div>
					</div>
					<div class="statistic-item">
						<div class="statistic-row">
							<span>上期应收金额总和</span>
							<span>{{ statisticTotal?.lastPeriodReceivableAmount ?? '-' }}元</span>
						</div>
						<div class="statistic-row">
							<span>上期实收金额总和</span>
							<span>{{ statisticTotal?.lastPeriodActualAmount ?? '-' }}元</span>
						</div>
					</div>
					<div class="statistic-item">
						<div class="statistic-row">
							<span>应收金额总和</span>
							<span>{{ statisticTotal?.totalReceivableAmount ?? '-' }}元</span>
						</div>
						<div class="statistic-row">
							<span>实收金额总和</span>
							<span>{{ statisticTotal?.totalActualAmount ?? '-' }}元</span>
						</div>
					</div>
					<div class="statistic-item">
						<div class="statistic-row">
							<span>上期回款率</span>
							<span>{{ statisticTotal?.lastPeriodReturnRate ?? '-' }}%</span>
						</div>
						<div class="statistic-row">
							<span>总回款率</span>
							<span>{{ statisticTotal?.totalReturnRate ?? '-' }}%</span>
						</div>
					</div>
				</div>
				<div class="wrap">
					<div class="cus-header">
						<el-form :model="formSearch" label-width="88px">
							<div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
								<el-form-item label="发电户号">
									<el-input v-model="formSearch.elecNo" placeholder="输入发电户号" clearable />
								</el-form-item>
								<el-form-item label="电站编号">
									<el-input v-model="formSearch.stationCode" placeholder="输入电站编号" clearable />
								</el-form-item>
								<el-form-item label="电站名称">
									<el-input v-model="formSearch.name" placeholder="输入电站名称" clearable />
								</el-form-item>
								<el-form-item label="状态">
									<el-select v-model="formSearch.statusList" multiple placeholder="选择状态" clearable style="width: 100%;">
										<el-option v-for="item in feeStatusOptions" :key="item.value" :label="item.label"
											:value="item.value" />
									</el-select>
								</el-form-item>
								<el-form-item v-if="activeTab === 'COMPANY'" label="对账单状态">
									<el-select v-model="formSearch.billStatus" placeholder="选择对账单状态" clearable style="width: 100%;">
										<el-option v-for="item in billStatusOptions" :key="item.value" :label="item.label"
											:value="item.value" />
									</el-select>
								</el-form-item>
								<!-- <el-form-item label="联系方式">
									<el-input v-model="formSearch.phone" placeholder="输入联系方式" clearable />
								</el-form-item> -->
								<el-form-item label="运维商">
									<el-input v-model="formSearch.opName" placeholder="输入运维商" clearable />
								</el-form-item>
								<el-form-item label="资方所属">
									<el-select v-model="formSearch.specialFlag" placeholder="选择资方所属" clearable style="width: 100%;">
										<el-option v-for="item in capitalBelongOptions" :key="item.value" :label="item.label"
											:value="item.value" />
									</el-select>
								</el-form-item>
								<el-form-item label="模式">
									<el-select v-model="formSearch.stationMode" placeholder="选择模式" clearable style="width: 100%;">
										<el-option v-for="item in pmDetailMode" :key="item.value" :label="item.label"
											:value="item.value" />
									</el-select>
								</el-form-item>
								<el-form-item label="项目公司">
									<el-input v-model="formSearch.projectCompanyName" placeholder="输入项目公司" clearable />
								</el-form-item>
								<el-form-item label="分中心">
									<el-select v-model="formSearch.subCenterCode" placeholder="选择分中心" clearable style="width: 100%;">
										<el-option v-for="item in subCenterOptions" :key="item.value" :label="item.label"
											:value="item.value" />
									</el-select>
								</el-form-item>
								<el-form-item label="省">
									<el-select v-model="formSearch.provinceId" @change="provinceChange" placeholder="省" clearable>
										<el-option v-for="(item, index) in provinceOptions" :value="item.id" :label="item.name"
											:key="index" />
									</el-select>
								</el-form-item>
								<el-form-item label="市">
									<el-select v-model="formSearch.cityId" @change="cityChange" placeholder="市" clearable>
										<el-option v-for="(item, index) in cityOptions" :value="item.id" :label="item.name" :key="index" />
									</el-select>
								</el-form-item>
								<el-form-item label="区">
									<el-select v-model="formSearch.regionId" placeholder="区" clearable>
										<el-option v-for="(item, index) in regionsOptions" :value="item.id" :label="item.name"
											:key="index" />
									</el-select>
								</el-form-item>
								<el-form-item label="开始月份">
									<el-date-picker v-model="formSearch.billStartMonth" type="month" placeholder="选择开始月份" clearable
										style="width: 100%;" value-format="YYYY-MM" />
								</el-form-item>
								<el-form-item label="结束月份">
									<el-date-picker v-model="formSearch.billEndMonth" type="month" placeholder="选择结束月份" clearable
										style="width: 100%;" value-format="YYYY-MM" />
								</el-form-item>
								<div class="search-buttons">
									<el-button type="default" @click="onReset">重置</el-button>
									<el-button type="primary" @click="queryList">查询</el-button>
									<el-link type="primary" @click="toggleExpand" :underline="false" style="margin-left: 10px;">
										{{ isExpanded ? '收起' : '展开' }}
										<el-icon>
											<arrow-up v-if="isExpanded" />
											<arrow-down v-else />
										</el-icon>
									</el-link>
								</div>
							</div>
						</el-form>
					</div>
					<div class="cus-main" ref="mainRef">
						<div class="cus-list" v-loading="loading" ref="cusListRef">
							<div style="text-align: right;margin-top: -10px; margin-bottom: 10px">
								<el-button type="primary" v-loading.fullscreen.lock="fullscreenLoading" element-loading-text="下载中..." plain @click="exportList">导出</el-button>
							</div>
							<el-table :data="listArr" class="cus-table" :empty-text="emptyText" border>
								<el-table-column fixed align="center" type="index" label="序号" width="60" resizable />
								<el-table-column align="center" prop="elecNo" label="发电户号" :width="160" resizable>
									<template #default="scope">
										{{ scope.row.elecNo || '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="stationCode" label="电站编号" :width="160" resizable>
									<template #default="scope">
										{{ scope.row.stationCode || '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="billDate" label="电费日期" :width="100" resizable>
									<template #default="scope">
										{{ scope.row.billDate || '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="elec" label="发电量(kWh)" :width="140" resizable>
									<template #default="scope">
										{{ scope.row.elec ?? '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="elecPrice" label="电费单价(元)" :width="140" resizable>
									<template #default="scope">
										{{ scope.row.elecPrice ?? '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="receivableAmount" label="应收电费(元)" :width="140" resizable>
									<template #default="scope">
										{{ scope.row.receivableAmount ?? '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="actualAmount" label="实收电费(元)" :width="140" resizable>
									<template #default="scope">
										{{ scope.row.actualAmount ?? '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="actualAmount" label="个人实收电费(元)" :width="140" resizable>
									<template #default="scope">
										{{ scope.row.personalActualAmount ?? '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="actualAmount" label="商户实收电费(元)" :width="140" resizable>
									<template #default="scope">
										{{ scope.row.spActualAmount ?? '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="actualAmount" label="回款率" :width="140" resizable>
									<template #default="scope">
										{{ scope.row.returnRate }}%
									</template>
								</el-table-column>
								<el-table-column align="center" prop="firstThreePowerAt" label="首次连续三天发电时间" :width="160" resizable>
									<template #default="scope">
										{{ scope.row.firstThreePowerAt || '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="totalElec" label="累计发电量(kWh)" :width="160" resizable>
									<template #default="scope">
										{{ scope.row.totalElec || '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="totalReceivableAmount" label="累计应收电费(元)" :width="160" resizable>
									<template #default="scope">
										{{ scope.row.totalReceivableAmount || '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="totalActualAmount" label="累计实收电费(元)" :width="160" resizable>
									<template #default="scope">
										{{ scope.row.totalActualAmount || '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="totalReturnRate" label="累计回款率" :width="160" resizable>
									<template #default="scope">
										{{ scope.row.totalReturnRate || '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="status" label="状态" :width="160" resizable>
									<template #default="scope">
										<el-tag :type="scope.row.status === 'NORMAL' ? 'primary' : 'danger'">{{ getDictLabel('electricity_fee_status', scope.row.status) }}</el-tag>
									</template>
								</el-table-column>
								<el-table-column v-if="activeTab === 'COMPANY'" align="center" prop="billStatus" label="对账单状态" :width="160" resizable>
									<template #default="scope">
										<el-tag v-if="scope.row.billStatus != null" :type="scope.row.billStatus === '1' ? 'primary' : 'danger'">{{ getDictLabel('bill_status', scope.row.billStatus) }}</el-tag>
									</template>
								</el-table-column>
								<el-table-column align="center" prop="stationMode" label="模式" width="120" resizable>
									<template #default="scope">
										{{ detailMode[scope.row.mode] || '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="specialFlag" label="资方所属" width="120" resizable>
									<template #default="scope">
										{{ pmSpecialFlag[scope.row.specialFlag] || '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="stationType" label="电站类型" width="120" resizable>
									<template #default="scope">
										{{ detStationType[scope.row.stationType] || '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="projectCompanyName" label="所属项目公司" width="150" resizable>
									<template #default="scope">
										{{ scope.row.projectCompanyName || '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="opName" label="运维商" width="150" resizable>
									<template #default="scope">
										{{ scope.row.opName || '-' }}
									</template>
								</el-table-column>
								<!-- <el-table-column align="center" prop="opType" label="运维商类别" width="120">
									<template #default="scope">
										{{ identityType[scope.row.opType] || '-' }}
									</template>
								</el-table-column> -->
								<el-table-column align="center" prop="subCenterName" label="所属分中心" width="120" resizable>
									<template #default="scope">
										{{ scope.row.subCenterName || '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="name" label="电站业主" width="120" resizable>
									<template #default="scope">
										{{ scope.row.name || '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="phone" label="业主联系方式" width="150" resizable>
									<template #default="scope">
										{{ scope.row.phone || '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="provinceName" label="省" width="120" resizable>
									<template #default="scope">
										{{ scope.row.provinceName || '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="cityName" label="市" width="120" resizable>
									<template #default="scope">
										{{ scope.row.cityName || '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="regionName" label="区域" width="120" resizable>
									<template #default="scope">
										{{ scope.row.regionName || '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="address" label="详细地址" width="200" resizable>
									<template #default="scope">
										{{ scope.row.address || '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="signStatus" label="光E宝/银联签约状态" width="160" resizable>
									<template #default="scope">
										{{ eSignStatus[scope.row.signStatus] || '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="stopStatus" label="终止状态" width="100" resizable>
									<template #default="scope">
										{{ stopStatusExtend[scope.row.stopStatus] || '-' }}
									</template>
								</el-table-column>
								<el-table-column fixed="right" align="center" label="操作" width="80" resizable>
									<template #default="scope">
										<el-button link type="primary" @click="goDetail(scope.row)">详情</el-button>
									</template>
								</el-table-column>
							</el-table>
							<el-pagination class="cus-pages" v-if="total" background layout="sizes, prev, pager, next, ->, total"
								:page-sizes="[10, 20, 30]" :page-size="pagination.pageSize" :current-page="pagination.pageNum"
								:total="total" @size-change="changeSize" @current-change="changeCurrent" />
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from 'vue';
import API from '@/api/maintainance';
import _API from '@/api/epc';
import _ from 'lodash';
import _D from '@/edata/_osp_data';
import { ElMessage,ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';
import { useTablePagination } from '@/composables/useTablePagination';
import { useDictStore } from '@/stores/modules/dict';

const dictStore = useDictStore();
const showEditDrawer = ref(false);
const capitalBelongOptions = _D.property;
const pmSpecialFlag = _D.pmSpecialFlag;
const subCenterOptions = _D.subCenterList;
const detStationType = _D.detStationType;
const detailMode = _D.detailMode;
const pmDetailMode = _D.pmDetailMode;
const identityType = _D.identityType;
const stopStatusExtend = _D.stopStatusExtend;
const eSignStatus = _D.eSignStatus

const isExpanded = ref(false);
const activeTab = ref('HOUSEHOLD');
const createTime = ref('');
const router = useRouter();

const statisticTotal = ref({
	lastPeriodElec: 0,
	lastPeriodActualAmount: 0,
	lastPeriodReceivableAmount: 0,
	lastPeriodReturnRate: 0,
	totalElec: 0,
	totalActualAmount: 0,
	totalReceivableAmount: 0,
	totalReturnRate: 0
});

const formSearch = reactive({
	billStartMonth: null,
	billEndMonth: null,
	elecNo: '',
	name: '',
	phone: '',
	opName: '',
	specialFlag: '',
	stationCode: null,
	subCenterCode: null,
	provinceId: '',
	cityId: '',
	regionId: '',
	statusList: null,
	stationMode: null,
	projectCompanyName: ''
});

const provinceOptions = ref([]);
const cityOptions = ref([]);
const regionsOptions = ref([]);
const addressList = ref({});
const emptyText = ref("暂无数据")
const fullscreenLoading = ref(false)

const {
	loading,
	listArr,
	total,
	pagination,
	getList,
	queryList,
	changeSize,
	changeCurrent,
} = useTablePagination(
	params => {
		getSummary();
		return API.getStationElecBillPage({ ...params, fieldMethod: activeTab.value, billStatus: activeTab.value === 'COMPANY' ? formSearch.billStatus : undefined })
	},
	() => formSearch,
	{ manual: true }
);

const getDictLabel = (dictType, value) => {
	const dictMap = dictStore.getDictMapByType(dictType);
	return dictMap[value] || '-';
};

const feeStatusOptions = computed(() => {
	return dictStore.getDictByType('electricity_fee_status');
});

const billStatusOptions = computed(() => {
	return dictStore.getDictByType('bill_status');
});

const onReset = _.throttle(
	() => {
		createTime.value = '';
		Object.assign(formSearch, {
			billStartMonth: null,
			billEndMonth: null,
			elecNo: '',
			name: '',
			phone: '',
			opName: '',
			specialFlag: '',
			stationCode: null,
			subCenterCode: null,
			provinceId: '',
			cityId: '',
			regionId: '',
			statusList: null,
			stationMode: null,
			projectCompanyName: ''
		});
		cityOptions.value = [];
		regionsOptions.value = [];
		queryList();
	},
	3000,
	{
		trailing: false
	}
);

const toggleExpand = () => {
	isExpanded.value = !isExpanded.value;
};

watch(activeTab, () => {
	queryList();
});

const getAreaList = () => {
	_API.getRegionList().then(res => {
		if (res.success && res.result) {
			for (let i in res.result.province_list) {
				provinceOptions.value.push({ id: i, name: res.result.province_list[i] });
			}
			addressList.value = res.result;
		}
	});
};

const provinceChange = (item) => {
	cityOptions.value = [];
	formSearch.cityId = '';
	formSearch.regionId = '';
	regionsOptions.value = [];
	if (!item) return;
	const str = item.substring(0, 2);
	for (let i in addressList.value.city_list) {
		if (str === String(i).substring(0, 2)) {
			cityOptions.value.push({ id: i, name: addressList.value.city_list[i] });
		}
	}
};

const cityChange = (item) => {
	regionsOptions.value = [];
	formSearch.regionId = '';
	if (!item) return;
	const str = item.substring(0, 4);
	for (let i in addressList.value.county_list) {
		if (str === String(i).substring(0, 4)) {
			regionsOptions.value.push({ id: i, name: addressList.value.county_list[i] });
		}
	}
};

const getSummary = () => {
	API.getStationElecBillSummary({ ...formSearch, fieldMethod: activeTab.value }).then(res => {
		if (res.success && res.result) {
			statisticTotal.value = res.result;
		}
	});
}

const goDetail = (row) => {
	router.push({
		path: '/maintainance/elecBill/detail',
		query: {
			stationCode: row.stationCode,
		}
	});
};

// 导出
const	exportList = _.debounce(
	function () {
		ElMessageBox.confirm('请确认是否导出此筛选条件下的列表?', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		})
			.then(() => {
					fullscreenLoading.value = true
					API.exportStationElecBill({...formSearch, fieldMethod: activeTab.value})
							.then(res => {
									let binaryData = [];
									let link = document.createElement('a');
									let date = new Date().getTime()
									binaryData.push(res);
									link.style.display = 'none';
									link.href = window.URL.createObjectURL(new Blob(binaryData));
									link.setAttribute('download', `电费报表_${date}.xlsx`);
									document.body.appendChild(link);
									link.click();
									document.body.removeChild(link);
							})
							.then(() => {
									fullscreenLoading.value = false
							})
							.catch(() => {
									fullscreenLoading.value = false
									ElMessage.error('导出失败');
							});
			})
			.catch(() => {
					// console.log('取消');
			});
	},
	300
)

onMounted(() => {
	getList();
	dictStore.fetchDict([
		'station_type',
		'electricity_fee_status',
		'bill_status'
	]);
	getAreaList();
});
</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';

:deep(.el-tabs__nav-scroll) {
	background-color: var(--el-bg-color);
	padding-left: 12px;
}

.cus-container {
	display: flex;
	gap: 20px;
	height: calc(100% - 54px);
	width: 100%;
}

.wrap {
	height: calc(100% - 150px);
	width: 100%;
}

.cus-header {
	margin-bottom: 0px;

	.form-item-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 12px;
		width: 100%;

		.el-form-item:nth-child(n+3):not(:last-child) {
			display: none;
		}

		&.is-expanded {
			.el-form-item:nth-child(n+3):not(:last-child) {
				display: flex;
			}
		}
	}

	.search-buttons {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		grid-column: -2 / -1;
	}

	.el-form-item {
		width: 100%;
		margin-bottom: 0px;

		.el-input,
		.el-select,
		.el-date-editor {
			width: 100%;
		}
	}
}

.statistic-container {
	display: flex;
	gap: 24px;
}

.statistic-item {
	padding: 15px 20px;
	color: #91929E;
	text-align: center;
	min-width: 160px;
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	background-color: var(--el-bg-color);
	flex: 1;


	.statistic-row {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-between;

		span {
			line-height: 1.4;

			&:first-child {
				font-size: 13px;
				opacity: 0.9;
				font-weight: 700;
			}

			&:last-child {
				font-size: 20px;
				font-weight: 700;
				color: rgb(143, 137, 137);
			}
		}
	}
}

.cus-main {
	display: flex;
	flex-direction: column;
	flex: 1;
	overflow: hidden;

	>.el-button {
		margin-bottom: 10px;
	}

	.cus-list {
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: hidden;
		position: relative;

		.cus-table {
			flex: 1;
			overflow: auto;
			height: 100%;
		}

		.cus-pages {
			margin-top: 10px;
		}
	}
}
</style>
