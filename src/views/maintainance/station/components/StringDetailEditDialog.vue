<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :before-close="handleClose"
    append-to-body
    destroy-on-close
  >
    <el-form ref="formRef" :model="form" label-width="80px">
      <div v-for="(mppt, mpptIndex) in form.mppts" :key="mpptIndex" class="mppt-block">
        <el-form-item
          :label="`MPPT ${mpptIndex + 1}`"
          :prop="`mppts[${mpptIndex}].name`"
          :rules="{ required: true, message: 'MPPT名称不能为空', trigger: 'blur' }"
        >
          <el-input v-model="mppt.name" placeholder="例如: mppt1" style="width: calc(100% - 100px); margin-right: 10px;" />
          <el-button type="danger" link @click="removeMppt(mpptIndex)" v-if="form.mppts.length > 1">删除</el-button>
        </el-form-item>
        <div v-for="(pv, pvIndex) in mppt.pv" :key="pvIndex" class="pv-block">
          <el-form-item
            :label="`PV ${getGlobalPvIndex(mpptIndex, pvIndex)}`"
            :prop="`mppts[${mpptIndex}].pv[${pvIndex}].name`"
            :rules="{ required: true, message: 'PV名称不能为空', trigger: 'blur' }"
          >
            <el-input v-model="pv.name" placeholder="例如: pv1" style="width: 150px; margin-right: 10px;" />
            <el-form-item
              label="数量"
              label-width="50px"
              :prop="`mppts[${mpptIndex}].pv[${pvIndex}].total`"
              :rules="{ type: 'number', min: 0, message: '数量必须为非负数', trigger: 'change' }"
              style="display: inline-flex; margin-bottom: 0;"
            >
              <el-input-number v-model="pv.total" :min="0" controls-position="right" />
            </el-form-item>
            <el-button type="danger" link @click="removePv(mpptIndex, pvIndex)" v-if="mppt.pv.length > 1" style="margin-left: 10px;">删除</el-button>
          </el-form-item>
        </div>
        <el-button @click="addPv(mpptIndex)" link type="primary" style="margin-left: 80px;">添加PV</el-button>
      </div>
      <el-button @click="addMppt" style="margin-top: 10px;">添加MPPT</el-button>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="isSubmitting">
          保存
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watchEffect } from 'vue';
import { ElMessage } from 'element-plus';
import _ from 'lodash';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:visible', 'submit']);

const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

const dialogTitle = computed(() => {
  return props.data && props.data.length > 0 ? '编辑组串' : '添加组串';
});

// 计算全局PV索引
const getGlobalPvIndex = (mpptIndex, pvIndex) => {
  let globalIndex = 0;
  // 累加前面所有MPPT的PV数量
  for (let i = 0; i < mpptIndex; i++) {
    globalIndex += form.value.mppts[i].pv ? form.value.mppts[i].pv.length : 0;
  }
  // 加上当前PV的索引（从1开始）
  return globalIndex + pvIndex + 1;
};

const formRef = ref(null);
const form = ref({ mppts: [] });
const isSubmitting = ref(false);

// 初始化表单数据的函数
const initFormData = () => {
  const data = props.data || [];
  if (data.length === 0) {
    // 新增时提供默认结构
    form.value.mppts = [{
      name: 'mppt1',
      total: 0,
      pv: [{ name: 'pv1', total: 0 }]
    }];
  } else {
    // 编辑时使用现有数据
    form.value.mppts = _.cloneDeep(data);
  }
};

// 使用 watchEffect 来确保数据同步
watchEffect(() => {
  if (props.visible) {
    initFormData();
  }
});

const handleClose = () => {
  emit('update:visible', false);
};

const addMppt = () => {
  // 计算所有MPPT下的PV总数量
  const totalPvCount = form.value.mppts.reduce((count, mppt) => {
    return count + (mppt.pv ? mppt.pv.length : 0);
  }, 0);

  form.value.mppts.push({
    name: `mppt${form.value.mppts.length + 1}`,
    total: 0,
    pv: [{ name: `pv${totalPvCount + 1}`, total: 0 }]
  });
};

const removeMppt = (index) => {
  form.value.mppts.splice(index, 1);
};

const addPv = (mpptIndex) => {
  // 计算所有MPPT下的PV总数量
  const totalPvCount = form.value.mppts.reduce((count, mppt) => {
    return count + (mppt.pv ? mppt.pv.length : 0);
  }, 0);

  const pvList = form.value.mppts[mpptIndex].pv;
  pvList.push({
    name: `pv${totalPvCount + 1}`,
    total: 0
  });
};

const removePv = (mpptIndex, pvIndex) => {
  const mppt = form.value.mppts[mpptIndex];
  if (mppt.pv.length > 1) {
    mppt.pv.splice(pvIndex, 1);
  } else {
    ElMessage.warning('每个MPPT至少需要保留一个PV');
  }
};

const handleSubmit = async () => {
  if (!formRef.value) return;
  try {
    const valid = await formRef.value.validate();
    if (valid) {
      // 校验MPPT不能为空
      if (!form.value.mppts || form.value.mppts.length === 0) {
        ElMessage.warning('至少需要添加一个MPPT');
        return;
      }

      // 校验每个MPPT至少有一个PV
      const invalidMppt = form.value.mppts.find(mppt => !mppt.pv || mppt.pv.length === 0);
      if (invalidMppt) {
        ElMessage.warning('每个MPPT至少需要包含一个PV');
        return;
      }

      // 校验每个PV的数量不能为0
      for (const mppt of form.value.mppts) {
        for (const pv of mppt.pv) {
          if (!pv.total || pv.total <= 0) {
            ElMessage.warning(`${pv.name} 的数量不能为0，请输入有效数量`);
            return;
          }
        }
      }

      isSubmitting.value = true;
      const finalData = form.value.mppts.map(mppt => {
        const total = mppt.pv.reduce((sum, pv) => sum + (pv.total || 0), 0);
        return { ...mppt, total };
      });
      emit('submit', finalData);
    } else {
      ElMessage.warning('请检查表单输入是否正确');
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

const resetSubmitting = () => {
  isSubmitting.value = false;
}

defineExpose({
  resetSubmitting
});
</script>

<style lang="less" scoped>
.mppt-block {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
  position: relative;
}
.pv-block {
  margin-left: 20px;
  padding-left: 10px;
  border-left: 2px solid #e4e7ed;
}
.dialog-footer {
  text-align: right;
}
</style>
